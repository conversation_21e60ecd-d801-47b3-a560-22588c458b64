# OIDC Quick Setup Guide

This guide will help you quickly set up OIDC authentication in your development environment.

## Prerequisites

- Identity Provider (IdP) running and accessible
- OIDC Client configured in your IdP
- Node.js and npm/yarn installed

## Quick Setup Steps

### 1. Environment Configuration

Copy the example environment file:
```bash
cp docs/example.env .env
```

Update the `.env` file with your specific values:
```bash
# Minimum required configuration
REACT_APP_OIDC_AUTHORITY=https://your-idp-url.com
REACT_APP_OIDC_CLIENT_ID=your_client_id
REACT_APP_OIDC_REDIRECT_URI=http://localhost:3000/callback
REACT_APP_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000/
REACT_APP_API_BASE_URL=https://your-api-url.com/api
```

### 2. Identity Provider Configuration

Configure your OIDC Identity Provider with these settings:

**Client Configuration:**
- **Client ID**: `your_client_id` (match your .env file)
- **Client Type**: Public (for SPA)
- **Grant Types**: Authorization Code
- **Response Types**: Code
- **PKCE**: Enabled (recommended)

**Redirect URIs:**
```
http://localhost:3000/callback
http://localhost:3000/silent-renew
```

**Post Logout Redirect URIs:**
```
http://localhost:3000/
```

**CORS Origins:**
```
http://localhost:3000
```

**Scopes:**
```
openid
profile
email
roles
identity_admin_api
```

### 3. Start the Application

```bash
npm start
```

The application will start at `http://localhost:3000`

### 4. Test Authentication

1. Navigate to `http://localhost:3000`
2. Click "Sign in with SSO"
3. You should be redirected to your Identity Provider
4. Sign in with your credentials
5. You should be redirected back to the application

## Troubleshooting Common Issues

### Issue: "Invalid redirect_uri"
**Solution**: Ensure the redirect URI in your .env file exactly matches what's configured in your IdP.

### Issue: CORS errors
**Solution**: Add `http://localhost:3000` to your IdP's CORS origins.

### Issue: "Invalid client_id"
**Solution**: Verify the client ID in your .env file matches exactly what's configured in your IdP.

### Issue: Token-related errors
**Solution**: Check that all required scopes are configured in your IdP.

## Verification Checklist

- [ ] Environment variables are set correctly
- [ ] Identity Provider client is configured
- [ ] Redirect URIs match exactly
- [ ] CORS is configured in IdP
- [ ] Required scopes are granted
- [ ] Application starts without errors
- [ ] Login redirects to IdP
- [ ] Successful login redirects back to app
- [ ] User information is displayed
- [ ] Logout works correctly

## Next Steps

Once basic authentication is working:

1. Configure role-based access control
2. Set up API authentication with bearer tokens
3. Configure production environment
4. Implement error handling and monitoring

## Development Tools

### Browser Developer Tools
- **Network Tab**: Monitor OIDC requests
- **Application Tab**: Check localStorage for tokens
- **Console**: View debug messages

### Useful URLs to Monitor
- `/.well-known/openid-configuration` - OIDC metadata
- `/authorize` - Authorization endpoint
- `/token` - Token endpoint
- `/userinfo` - User information endpoint

### Debug Mode
The application automatically enables debug logging in development mode. Check the browser console for detailed authentication information.

## Support

For detailed information, see the main documentation: [SSO_OIDC_Integration.md](./SSO_OIDC_Integration.md)

Common resources:
- [OpenID Connect Specification](https://openid.net/connect/)
- [OIDC-Client-TS Documentation](https://github.com/authts/oidc-client-ts)
- [React-OIDC-Context Documentation](https://github.com/authts/react-oidc-context) 