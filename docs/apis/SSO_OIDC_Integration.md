# SSO Integration with OIDC-Client-TS

This document describes how to integrate Single Sign-On (SSO) using OpenID Connect (OIDC) with the `oidc-client-ts` library in this React application.

## Table of Contents

1. [Overview](#overview)
2. [Configuration](#configuration)
3. [Environment Variables](#environment-variables)
4. [How to Login](#how-to-login)
5. [How to Logout](#how-to-logout)
6. [How to Get User Information](#how-to-get-user-information)
7. [Protected Routes](#protected-routes)
8. [Silent Token Renewal](#silent-token-renewal)
9. [Error Handling](#error-handling)
10. [Debugging](#debugging)

## Overview

This application uses:
- **oidc-client-ts** (v3.2.1): Core OIDC client library
- **react-oidc-context** (v3.3.0): React context provider for OIDC

The authentication flow follows the Authorization Code flow with PKCE, which is the recommended approach for single-page applications.

## Configuration

### OIDC Configuration

The OIDC client is configured in `src/utils/authConfig.ts`:

```typescript
import { AuthProviderProps } from 'react-oidc-context';
import { WebStorageStateStore } from 'oidc-client-ts';
import { env } from './env';

export const oidcConfig: AuthProviderProps = {
  authority: env.oidcAuthority,                    // Identity provider URL
  client_id: env.oidcClientId,                     // Client ID from IdP
  redirect_uri: env.oidcRedirectUri,               // Callback URL after login
  post_logout_redirect_uri: env.oidcPostLogoutRedirectUri, // URL after logout
  response_type: 'code',                           // Authorization code flow
  scope: 'openid profile email roles identity_admin_api', // Requested scopes
  automaticSilentRenew: true,                      // Enable automatic token renewal
  loadUserInfo: true,                              // Load user info from UserInfo endpoint
  monitorSession: true,                            // Monitor user session
  stateStore: new WebStorageStateStore({ store: window.localStorage }),
  userStore: new WebStorageStateStore({ store: window.localStorage }),
  metadataUrl: `${env.oidcAuthority}/.well-known/openid-configuration`,
  response_mode: 'fragment',                       // Response mode for SPA
  silent_redirect_uri: `${env.oidcRedirectUri.replace('/callback', '/silent-renew')}`,
  extraQueryParams: {},                            // Additional query parameters
  revokeTokensOnSignout: true,                     // Revoke tokens on logout
};
```

### Key Configuration Options

- **authority**: Your OIDC Identity Provider URL
- **client_id**: The client identifier registered with your IdP
- **redirect_uri**: URL where users are redirected after authentication
- **scope**: Permissions requested from the identity provider
- **automaticSilentRenew**: Automatically renews tokens before expiration
- **loadUserInfo**: Fetches additional user claims from the UserInfo endpoint

## Environment Variables

Create a `.env` file in your project root with the following variables:

```bash
# OIDC Configuration
REACT_APP_OIDC_AUTHORITY=https://your-identity-provider.com
REACT_APP_OIDC_CLIENT_ID=your_client_id
REACT_APP_OIDC_REDIRECT_URI=http://localhost:3000/callback
REACT_APP_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000/

# API Configuration
REACT_APP_API_BASE_URL=https://your-api.com/api
```

### Example .env for Different Environments

#### Development
```bash
# Development Environment
REACT_APP_OIDC_AUTHORITY=https://localhost:44310
REACT_APP_OIDC_CLIENT_ID=identity_admin_dev
REACT_APP_OIDC_REDIRECT_URI=http://localhost:3000/callback
REACT_APP_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000/
REACT_APP_API_BASE_URL=https://localhost:44302/api
```

#### Production
```bash
# Production Environment
REACT_APP_OIDC_AUTHORITY=https://auth.yourcompany.com
REACT_APP_OIDC_CLIENT_ID=identity_admin_prod
REACT_APP_OIDC_REDIRECT_URI=https://admin.yourcompany.com/callback
REACT_APP_OIDC_POST_LOGOUT_REDIRECT_URI=https://admin.yourcompany.com/
REACT_APP_API_BASE_URL=https://api.yourcompany.com/api
```

## How to Login

### 1. Using the Auth Context

The easiest way to trigger login is through the `useAuth` hook:

```typescript
import { useAuth } from '../../contexts/AuthContext';

const LoginComponent = () => {
  const { login, isLoading } = useAuth();

  const handleLogin = () => {
    login(); // This triggers the OIDC login flow
  };

  return (
    <button onClick={handleLogin} disabled={isLoading}>
      {isLoading ? 'Signing in...' : 'Sign in with SSO'}
    </button>
  );
};
```

### 2. Login Flow

1. User clicks login button
2. Application redirects to Identity Provider
3. User authenticates with IdP
4. IdP redirects back to your app with authorization code
5. `AuthCallback` component handles the callback and exchanges code for tokens
6. User is redirected to the intended page or dashboard

### 3. Callback Handling

The `AuthCallback` component (`src/components/auth/AuthCallback.tsx`) handles the return from the identity provider:

```typescript
// This component automatically:
// 1. Processes the authorization code
// 2. Exchanges it for tokens
// 3. Stores tokens securely
// 4. Redirects user to intended destination
```

## How to Logout

### 1. Using the Auth Context

```typescript
import { useAuth } from '../../contexts/AuthContext';

const LogoutComponent = () => {
  const { logout, isAuthenticated } = useAuth();

  const handleLogout = () => {
    logout(); // This triggers the OIDC logout flow
  };

  if (!isAuthenticated) return null;

  return (
    <button onClick={handleLogout}>
      Sign Out
    </button>
  );
};
```

### 2. Logout Flow

1. User clicks logout button
2. Application revokes tokens with IdP (if `revokeTokensOnSignout: true`)
3. Application redirects to IdP logout endpoint
4. IdP clears its session
5. User is redirected to `post_logout_redirect_uri`

### 3. Force Logout (Error Scenarios)

For error scenarios, you can trigger a custom logout event:

```typescript
// Trigger custom logout event
const forceLogout = () => {
  window.dispatchEvent(new Event('userSignout'));
};
```

## How to Get User Information

### 1. Basic User Info

```typescript
import { useAuth } from '../../contexts/AuthContext';

const UserProfile = () => {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated || !user) {
    return <div>Not authenticated</div>;
  }

  return (
    <div>
      <h2>User Profile</h2>
      <p>Name: {user.profile.name}</p>
      <p>Email: {user.profile.email}</p>
      <p>Username: {user.profile.preferred_username}</p>
      <p>Subject: {user.profile.sub}</p>
    </div>
  );
};
```

### 2. Available User Properties

The user object contains:
- `user.profile.sub` - Subject identifier
- `user.profile.name` - Full name
- `user.profile.given_name` - First name
- `user.profile.family_name` - Last name
- `user.profile.email` - Email address
- `user.profile.preferred_username` - Username
- `user.profile.roles` - User roles (if included in scope)
- `user.access_token` - Access token for API calls
- `user.refresh_token` - Refresh token
- `user.expires_at` - Token expiration timestamp

### 3. Using User Info in API Calls

```typescript
import { useAuth } from '../../contexts/AuthContext';

const ApiCallComponent = () => {
  const { user } = useAuth();

  const callAPI = async () => {
    if (!user?.access_token) return;

    const response = await fetch('/api/protected-endpoint', {
      headers: {
        'Authorization': `Bearer ${user.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    return data;
  };

  return (
    <button onClick={callAPI}>
      Call Protected API
    </button>
  );
};
```

## Protected Routes

### 1. Using ProtectedRoute Component

```typescript
import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import { Dashboard } from '../pages/dashboard/Dashboard';

// Wrap protected components
<ProtectedRoute>
  <Dashboard />
</ProtectedRoute>
```

### 2. Role-Based Access

```typescript
import { RoleGuard } from '../components/auth/RoleGuard';

<RoleGuard requiredRoles={['admin', 'manager']}>
  <AdminPanel />
</RoleGuard>
```

### 3. Programmatic Authentication Check

```typescript
import { useAuth } from '../../contexts/AuthContext';

const MyComponent = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Please log in</div>;

  return <div>Protected content</div>;
};
```

## Silent Token Renewal

The application automatically renews tokens before expiration:

### Configuration
```typescript
// In authConfig.ts
automaticSilentRenew: true,
silent_redirect_uri: `${env.oidcRedirectUri.replace('/callback', '/silent-renew')}`,
```

### Silent Renew Route
Add this route to your router:
```typescript
<Route path="/silent-renew" element={<SilentRenew />} />
```

### How it Works
1. Before token expiration, the library creates a hidden iframe
2. Iframe navigates to the silent renewal endpoint
3. IdP issues new tokens without user interaction
4. New tokens are stored automatically

## Error Handling

### 1. Authentication Errors

The `AuthCallback` component handles various error scenarios:

```typescript
// Common error scenarios:
// - Invalid state parameter
// - Expired authorization code
// - Network connectivity issues
// - IdP configuration problems
```

### 2. Token Expiration

```typescript
import { useAuth } from 'react-oidc-context';

const { error } = useAuth();

useEffect(() => {
  if (error) {
    console.error('OIDC Error:', error.message);
    // Handle specific error types
    if (error.message.includes('token')) {
      // Token-related error
    }
  }
}, [error]);
```

### 3. Clear Auth State

Use the utility function to clear corrupted auth state:

```typescript
import { clearAuthState } from '../utils/authUtils';

// Clear all authentication data
clearAuthState();
```

## Debugging

### 1. Enable Debug Logging

In development mode, debug information is automatically logged:

```typescript
// In AuthContext.tsx
useEffect(() => {
  if (process.env.NODE_ENV === 'development') {
    logOidcConfig();
    setTimeout(() => {
      debugAuthState();
    }, 1000);
  }
}, []);
```

### 2. Check Browser Storage

Authentication data is stored in:
- **localStorage**: User profile, tokens
- **sessionStorage**: Temporary state data

### 3. Verify IdP Configuration

Ensure your Identity Provider is configured with:
- Correct client ID
- Proper redirect URIs
- Appropriate scopes
- CORS settings for your domain

### 4. Network Tab

Monitor network requests to:
- `.well-known/openid-configuration` - Metadata endpoint
- `/authorize` - Authorization endpoint
- `/token` - Token endpoint
- `/userinfo` - User info endpoint

## Common Issues and Solutions

### Issue: "Invalid state" error
**Solution**: Clear auth state and try again
```typescript
clearAuthState();
```

### Issue: CORS errors
**Solution**: Configure CORS on your Identity Provider to allow your domain

### Issue: Token renewal fails
**Solution**: Check that silent renewal URL is properly configured and accessible

### Issue: User info not loading
**Solution**: Ensure `loadUserInfo: true` and proper scopes are configured

## Required Identity Provider Configuration

Your OIDC Identity Provider must be configured with:

1. **Client Settings**:
   - Client ID: `identity_admin` (or your chosen ID)
   - Client Type: Public (for SPA) or Confidential (with PKCE)
   - Grant Types: Authorization Code
   - Response Types: Code

2. **Redirect URIs**:
   - Login: `http://localhost:3000/callback`
   - Logout: `http://localhost:3000/`
   - Silent Renew: `http://localhost:3000/silent-renew`

3. **CORS Origins**:
   - `http://localhost:3000` (development)
   - Your production domain

4. **Scopes**:
   - `openid` (required)
   - `profile`
   - `email`
   - `roles`
   - Custom API scopes as needed

## Security Considerations

1. **Always use HTTPS in production**
2. **Store sensitive data securely** (tokens are stored in localStorage)
3. **Implement proper CORS policies**
4. **Use appropriate token lifetimes**
5. **Monitor for token leakage**
6. **Implement proper error handling**
7. **Use PKCE for additional security**

## Support

For issues related to:
- **OIDC Configuration**: Check your Identity Provider documentation
- **Token Issues**: Verify scopes and client configuration
- **Network Issues**: Check CORS and firewall settings
- **Application Issues**: Check browser console and network tab 