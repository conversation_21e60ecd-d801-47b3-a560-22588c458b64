# PowerShell script to create self-signed certificate for localhost:3000
Write-Host "Creating self-signed certificate for localhost:3000..." -ForegroundColor Green

# Create certificates directory if it doesn't exist
$certDir = "certificates"
if (!(Test-Path $certDir)) {
    New-Item -ItemType Directory -Path $certDir
    Write-Host "Created certificates directory" -ForegroundColor Yellow
}

# Generate self-signed certificate
$cert = New-SelfSignedCertificate `
    -DnsName "localhost" `
    -CertStoreLocation "cert:\LocalMachine\My" `
    -KeyAlgorithm RSA `
    -KeyLength 2048 `
    -HashAlgorithm SHA256 `
    -NotAfter (Get-Date).AddYears(1) `
    -Subject "CN=localhost" `
    -FriendlyName "localhost development certificate"

Write-Host "Certificate created with thumbprint: $($cert.Thumbprint)" -ForegroundColor Green

# Export certificate to PEM format
$certPath = "$certDir\localhost.crt"
$keyPath = "$certDir\localhost.key"

# Export certificate
Export-Certificate -Cert $cert -FilePath "$certDir\localhost.cer" -Type CERT
certutil -encode "$certDir\localhost.cer" $certPath
Remove-Item "$certDir\localhost.cer"

# Export private key (requires manual steps)
Write-Host "Certificate exported to: $certPath" -ForegroundColor Green
Write-Host "To export private key:" -ForegroundColor Yellow
Write-Host "1. Run: certlm.msc" -ForegroundColor White
Write-Host "2. Navigate to Personal > Certificates" -ForegroundColor White
Write-Host "3. Find certificate with subject 'localhost'" -ForegroundColor White
Write-Host "4. Right-click > All Tasks > Export..." -ForegroundColor White
Write-Host "5. Export with private key as PFX" -ForegroundColor White
Write-Host "6. Convert PFX to PEM using OpenSSL" -ForegroundColor White

Write-Host "Or use mkcert for easier certificate generation:" -ForegroundColor Cyan
Write-Host "1. Install mkcert: choco install mkcert" -ForegroundColor White
Write-Host "2. Run: mkcert -install" -ForegroundColor White
Write-Host "3. Run: mkcert localhost" -ForegroundColor White
