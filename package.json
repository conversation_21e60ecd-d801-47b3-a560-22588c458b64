{"name": "pdf-ocr-dashboard", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "node server.js", "build": "next build", "start": "node server.js", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@tanstack/react-query": "^5.81.2", "@tanstack/react-virtual": "^3.13.9", "@types/react-dropzone": "^4.2.2", "@types/react-query": "^1.2.8", "@types/react-router-dom": "^5.3.3", "axios": "^1.10.0", "critters": "^0.0.23", "next": "^15.3.3", "node-forge": "^1.3.1", "oidc-client-ts": "^3.2.1", "pdfjs-dist": "^5.3.31", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-oidc-context": "^3.3.0", "react-router-dom": "^7.6.2", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/estree": "^1.0.8", "@types/node": "^20.19.0", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}