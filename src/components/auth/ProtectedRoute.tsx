import { useEffect, useState } from "react";

import { useAuth } from "@/contexts/AuthContext";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { UnauthorizedMessage } from "@/components/ui/UnauthorizedMessage";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
  requireAll?: boolean; // If true, user must have ALL roles/permissions
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  requireAll = false,
  fallback,
}) => {
  const { 
    isAuthenticated, 
    isLoading, 
    user, 
    login, 
    error,
    hasRole,
    hasAnyRole,
    getUserRoles,
    isTokenExpired
  } = useAuth();
  
  const [isChecking, setIsChecking] = useState(true);
  const [accessDenied, setAccessDenied] = useState(false);

  console.log("ProtectedRoute render", { 
    isAuthenticated, 
    isLoading, 
    hasUser: !!user,
    userRoles: user ? getUserRoles() : [],
    requiredRoles,
    isTokenExpired: user ? isTokenExpired() : null
  });

  useEffect(() => {
    const checkAccess = async () => {
      // Wait for authentication to complete
      if (isLoading) return;
      
      // If there's an authentication error, show it
      if (error) {
        setIsChecking(false);
        setAccessDenied(true);
        return;
      }

      // If not authenticated, initiate login
      if (!isAuthenticated) {
        console.log("🔐 User not authenticated, initiating login...");
        try {
          await login();
        } catch (error) {
          console.error("🚨 Login initiation failed:", error);
          setIsChecking(false);
          setAccessDenied(true);
        }
        return;
      }

      // Check if token is expired
      if (user && isTokenExpired()) {
        console.log("🚨 Token expired, need to re-authenticate");
        try {
          await login();
        } catch (error) {
          console.error("🚨 Re-authentication failed:", error);
          setIsChecking(false);
          setAccessDenied(true);
        }
        return;
      }

      // Check role requirements
      if (requiredRoles.length > 0) {
        const hasRequiredRoles = requireAll
          ? requiredRoles.every(role => hasRole(role))
          : hasAnyRole(requiredRoles);
        
        if (!hasRequiredRoles) {
          console.log("🚨 Access denied - insufficient roles:", {
            userRoles: getUserRoles(),
            requiredRoles,
            requireAll
          });
          setAccessDenied(true);
          setIsChecking(false);
          return;
        }
      }

      // Check permission requirements
      if (requiredPermissions.length > 0 && user) {
        // Note: This is a placeholder for permission checking
        // You'll need to implement permission checking based on your user structure
        const userPermissions = user.profile?.permissions || [];
        const hasRequiredPermissions = requireAll
          ? requiredPermissions.every(permission => userPermissions.includes(permission))
          : requiredPermissions.some(permission => userPermissions.includes(permission));
          
        if (!hasRequiredPermissions) {
          console.log("🚨 Access denied - insufficient permissions:", {
            userPermissions,
            requiredPermissions,
            requireAll
          });
          setAccessDenied(true);
          setIsChecking(false);
          return;
        }
      }

      // All checks passed
      setAccessDenied(false);
      setIsChecking(false);
    };

    checkAccess();
  }, [
    isAuthenticated,
    isLoading,
    user,
    login,
    requiredRoles,
    requiredPermissions,
    requireAll,
    error,
    hasRole,
    hasAnyRole,
    getUserRoles,
    isTokenExpired,
  ]);

  // Show loading spinner while checking authentication or access
  if (isLoading || isChecking) {
    return fallback || <LoadingSpinner />;
  }

  // Show error message if there's an authentication error
  if (error) {
    return (
      <UnauthorizedMessage 
        message={`Authentication error: ${error.message || 'Unknown error'}`} 
      />
    );
  }

  // If not authenticated (and login was attempted), show nothing
  // The login redirect should be in progress
  if (!isAuthenticated) {
    return null;
  }

  // Show access denied message if user doesn't have required access
  if (accessDenied) {
    let message = "Access denied.";
    
    if (requiredRoles.length > 0) {
      message += ` This page requires ${requireAll ? 'all of' : 'one of'} the following roles: ${requiredRoles.join(", ")}.`;
    }
    
    if (requiredPermissions.length > 0) {
      message += ` This page requires ${requireAll ? 'all of' : 'one of'} the following permissions: ${requiredPermissions.join(", ")}.`;
    }

    return (
      <UnauthorizedMessage 
        message={message}
      />
    );
  }

  // All checks passed, render children
  return <>{children}</>;
};
