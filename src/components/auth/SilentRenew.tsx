import { useEffect } from 'react';
import { UserManager, UserManagerSettings } from 'oidc-client-ts';
import { oidcConfig } from '@/lib/oidcConfig';
import { handleAuthError } from '@/lib/authUtils';

/**
 * Silent token renewal component
 * This component is used in an iframe for silent token renewal
 */
export const SilentRenew: React.FC = () => {
  useEffect(() => {
    const handleSilentRenew = async () => {
      try {
        console.log('🔄 Processing silent token renewal...');
        
        const userManager = new UserManager(oidcConfig as UserManagerSettings);
        const user = await userManager.signinSilentCallback();
        
        if (user) {
          console.log('✅ Silent token renewal successful:', {
            expires_at: user.expires_at ? new Date(user.expires_at * 1000).toISOString() : null,
            scope: user.scope,
          });
        } else {
          console.warn('⚠️ Silent renewal completed but no user returned');
        }
      } catch (error) {
        console.error('❌ Silent token renewal failed:', error);
        handleAuthError(error as Error);
      }
    };

    handleSilentRenew();
  }, []);

  // This component should be invisible
  return null;
}; 