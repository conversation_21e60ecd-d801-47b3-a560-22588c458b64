import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { UnauthorizedMessage } from "@/components/ui/UnauthorizedMessage";

interface RoleGuardProps {
  children: React.ReactNode;
  requiredRoles: string[];
  requireAll?: boolean; // If true, user must have ALL roles; if false, user needs ANY role
  fallback?: React.ReactNode;
  showUnauthorized?: boolean; // If false, returns null instead of unauthorized message
}

export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  requiredRoles,
  requireAll = false,
  fallback,
  showUnauthorized = true,
}) => {
  const { isAuthenticated, isLoading, user, hasRole, hasAnyRole } = useAuth();

  // Show loading spinner while authentication is in progress
  if (isLoading) {
    return fallback || <LoadingSpinner />;
  }

  // If user is not authenticated, don't show anything
  // The parent ProtectedRoute should handle authentication
  if (!isAuthenticated) {
    return null;
  }

  // Check role requirements
  const hasRequiredRoles = requireAll 
    ? requiredRoles.every(role => hasRole(role))
    : hasAnyRole(requiredRoles);

  // User doesn't have required roles
  if (!hasRequiredRoles) {
    if (!showUnauthorized) {
      return null;
    }

    return fallback || (
      <UnauthorizedMessage
        message={`Access denied. This section requires ${requireAll ? 'all of' : 'one of'} the following roles: ${requiredRoles.join(", ")}`}
      />
    );
  }

  // User has required roles, render children
  return <>{children}</>;
};

// Utility component for checking a single role
interface SingleRoleGuardProps {
  children: React.ReactNode;
  requiredRole: string;
  fallback?: React.ReactNode;
  showUnauthorized?: boolean;
}

export const SingleRoleGuard: React.FC<SingleRoleGuardProps> = ({
  children,
  requiredRole,
  fallback,
  showUnauthorized = true,
}) => {
  return (
    <RoleGuard
      requiredRoles={[requiredRole]}
      fallback={fallback}
      showUnauthorized={showUnauthorized}
    >
      {children}
    </RoleGuard>
  );
};

// Utility component for admin-only content
interface AdminGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUnauthorized?: boolean;
}

export const AdminGuard: React.FC<AdminGuardProps> = ({
  children,
  fallback,
  showUnauthorized = true,
}) => {
  return (
    <RoleGuard
      requiredRoles={["admin", "administrator"]}
      requireAll={false}
      fallback={fallback}
      showUnauthorized={showUnauthorized}
    >
      {children}
    </RoleGuard>
  );
}; 