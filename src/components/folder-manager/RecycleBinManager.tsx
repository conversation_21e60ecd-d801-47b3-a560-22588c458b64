"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { 
  FolderIcon, 
  DocumentIcon, 
  ArrowPathIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  Bars3Icon,
  ViewColumnsIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { RecycleBinService, DeletedItemDto, DeletedItemType } from '@/api/services/recycleBinService';
import { ApiClient } from '@/api/core/apiClient';
import {
  SortDirection,
  PaginationInfo
} from '@/api/types/interfaces';

interface RecycleBinManagerProps {
  apiClient: ApiClient;
}

interface RecycleBinState {
  deletedItems: DeletedItemDto[];
  selectedItems: Set<string>;
  loading: boolean;
  error: string | null;
  searchTerm: string;
  sorting: {
    field: 'Name' | 'DeletedAt' | 'ExpiresAt' | 'FileSize';
    direction: SortDirection;
  };
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  };
  viewMode: 'list' | 'grid';
  showBulkActions: boolean;
}

export function RecycleBinManager({ apiClient }: RecycleBinManagerProps) {
  const [state, setState] = useState<RecycleBinState>({
    deletedItems: [],
    selectedItems: new Set(),
    loading: false,
    error: null,
    searchTerm: "",
    sorting: {
      field: 'DeletedAt',
      direction: SortDirection.DESC
    },
    pagination: {
      page: 1,
      pageSize: 20,
      totalItems: 0,
      totalPages: 0
    },
    viewMode: 'list',
    showBulkActions: false
  });

  const recycleBinService = new RecycleBinService(apiClient);

  // Load deleted items
  const loadDeletedItems = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await recycleBinService.getDeletedItems({
        page: state.pagination.page,
        pageSize: state.pagination.pageSize,
        search: state.searchTerm || undefined,
        sortBy: state.sorting.field,
        sortDirection: state.sorting.direction
      });

      setState(prev => ({
        ...prev,
        deletedItems: response.items || [],
        pagination: {
          ...prev.pagination,
          totalItems: response.pagination?.totalItems || 0,
          totalPages: response.pagination?.totalPages || 1
        },
        loading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load deleted items',
        loading: false
      }));
    }
  }, [recycleBinService, state.pagination.page, state.pagination.pageSize, state.sorting, state.searchTerm]);

  // Initialize component
  useEffect(() => {
    loadDeletedItems();
  }, [loadDeletedItems]);

  // Handle search
  const handleSearch = useCallback(() => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page: 1 }
    }));
    loadDeletedItems();
  }, [loadDeletedItems]);

  // Handle sorting
  const handleSort = (field: 'Name' | 'DeletedAt' | 'ExpiresAt' | 'FileSize') => {
    setState(prev => ({
      ...prev,
      sorting: {
        field,
        direction: prev.sorting.field === field && prev.sorting.direction === SortDirection.ASC
          ? SortDirection.DESC
          : SortDirection.ASC
      },
      pagination: { ...prev.pagination, page: 1 }
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page }
    }));
  };

  // Handle item selection
  const toggleItemSelection = (itemId: string) => {
    setState(prev => {
      const newSelection = new Set(prev.selectedItems);
      if (newSelection.has(itemId)) {
        newSelection.delete(itemId);
      } else {
        newSelection.add(itemId);
      }
      return { 
        ...prev, 
        selectedItems: newSelection,
        showBulkActions: newSelection.size > 0
      };
    });
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    setState(prev => {
      const newSelection = new Set<string>();
      if (checked) {
        prev.deletedItems.forEach(item => newSelection.add(item.id));
      }
      return { 
        ...prev, 
        selectedItems: newSelection,
        showBulkActions: newSelection.size > 0
      };
    });
  };

  // Restore items
  const handleRestore = async (itemIds: string[]) => {
    try {
      // Use bulk restore for multiple items
      if (itemIds.length > 1) {
        await recycleBinService.bulkRestore({
          itemIds,
          handleConflicts: 'rename'
        });
      } else {
        // Use single item restore for one item
        await recycleBinService.restoreItem(itemIds[0], {
          handleConflicts: 'rename'
        });
      }

      setState(prev => ({ 
        ...prev, 
        selectedItems: new Set(),
        showBulkActions: false
      }));
      loadDeletedItems();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to restore items'
      }));
    }
  };

  // Permanently delete items
  const handlePermanentDelete = async (itemIds: string[]) => {
    try {
      // Use bulk delete for multiple items
      if (itemIds.length > 1) {
        await recycleBinService.bulkPermanentlyDelete({
          itemIds,
          confirm: true
        });
      } else {
        // Use single item delete for one item
        await recycleBinService.permanentlyDeleteItem(itemIds[0]);
      }

      setState(prev => ({ 
        ...prev, 
        selectedItems: new Set(),
        showBulkActions: false
      }));
      loadDeletedItems();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to permanently delete items'
      }));
    }
  };

  // Format file size
  const formatFileSize = (bytes?: number) => {
    if (!bytes || bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  };

  return (
    <div className="min-h-screen bg-gray-50 animate-fade-in">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-heading-2 text-gray-900">Thùng Rác</h1>
              <p className="text-body text-gray-600 mt-1">
                Quản lý các files và folders đã xóa
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={() => loadDeletedItems()}
                className="btn-secondary flex items-center gap-2"
              >
                <ArrowPathIcon className="w-4 h-4" />
                Làm mới
              </button>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {state.error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
            <div className="flex">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{state.error}</p>
              </div>
              <div className="ml-auto pl-3">
                <button
                  onClick={() => setState(prev => ({ ...prev, error: null }))}
                  className="text-red-400 hover:text-red-600"
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Search */}
        <div className="card p-6 mb-6 animate-fade-in">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Tìm kiếm files và folders đã xóa"
                  value={state.searchTerm}
                  onChange={(e) => setState(prev => ({ ...prev, searchTerm: e.target.value }))}
                  className="input pl-10 w-full"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <button 
              onClick={handleSearch}
              className="btn-primary"
            >
              Tìm kiếm
            </button>
          </div>
        </div>

        {/* Toolbar */}
        <div className="flex justify-between items-center mb-6 animate-fade-in">
          <div className="flex items-center gap-3">
            {state.showBulkActions && (
              <div className="flex items-center gap-2 animate-slide-in">
                <span className="text-sm text-gray-600">
                  {state.selectedItems.size} đã chọn
                </span>
                <button 
                  onClick={() => handleRestore(Array.from(state.selectedItems))}
                  className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors"
                  title="Khôi phục"
                >
                  <ArrowPathIcon className="w-4 h-4" />
                </button>
                <button 
                  onClick={() => handlePermanentDelete(Array.from(state.selectedItems))}
                  className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                  title="Xóa vĩnh viễn"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setState(prev => ({
                ...prev,
                viewMode: prev.viewMode === 'list' ? 'grid' : 'list'
              }))}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              title={state.viewMode === 'list' ? 'Chế độ lưới' : 'Chế độ danh sách'}
            >
              {state.viewMode === 'list' ? 
                <ViewColumnsIcon className="w-4 h-4" /> : 
                <Bars3Icon className="w-4 h-4" />
              }
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="card animate-fade-in">
          {state.viewMode === 'list' ? (
            <>
              {/* Table Header */}
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
                  <div className="col-span-1">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      checked={state.selectedItems.size > 0 && state.selectedItems.size === state.deletedItems.length}
                    />
                  </div>
                  <div className="col-span-1">Loại</div>
                  <div className="col-span-3">
                    <button
                      onClick={() => handleSort('Name')}
                      className="flex items-center gap-1 hover:text-gray-900 transition-colors"
                    >
                      Tên
                      {state.sorting.field === 'Name' && (
                        <span className="text-xs">
                          {state.sorting.direction === SortDirection.ASC ? '↑' : '↓'}
                        </span>
                      )}
                    </button>
                  </div>
                  <div className="col-span-2">Người xóa</div>
                  <div className="col-span-2">
                    <button
                      onClick={() => handleSort('DeletedAt')}
                      className="flex items-center gap-1 hover:text-gray-900 transition-colors"
                    >
                      Ngày xóa
                      {state.sorting.field === 'DeletedAt' && (
                        <span className="text-xs">
                          {state.sorting.direction === SortDirection.ASC ? '↑' : '↓'}
                        </span>
                      )}
                    </button>
                  </div>
                  <div className="col-span-1">Kích thước</div>
                  <div className="col-span-2">Thao tác</div>
                </div>
              </div>

              {/* Items */}
              <div className="divide-y divide-gray-200">
                {state.loading ? (
                  <div className="px-6 py-12 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-sm text-gray-600">Đang tải...</p>
                  </div>
                ) : (
                  <>
                    {/* Deleted Items */}
                    {state.deletedItems.map((item, index) => (
                      <div 
                        key={item.id} 
                        className="px-6 py-4 hover:bg-gray-50 animate-fade-in transition-colors"
                        style={{ animationDelay: `${index * 0.05}s` }}
                      >
                        <div className="grid grid-cols-12 gap-4 items-center">
                          <div className="col-span-1">
                            <input
                              type="checkbox"
                              checked={state.selectedItems.has(item.id)}
                              onChange={() => toggleItemSelection(item.id)}
                              className="rounded border-gray-300"
                            />
                          </div>
                          <div className="col-span-1">
                            {item.itemType === DeletedItemType.Folder ? (
                              <FolderIcon className="w-6 h-6 text-gray-400" />
                            ) : (
                              <DocumentIcon className="w-6 h-6 text-gray-400" />
                            )}
                          </div>
                          <div className="col-span-3">
                            <p className="font-medium text-gray-700">{item.name}</p>
                            <p className="text-sm text-gray-500 mt-1">{item.originalPath}</p>
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {item.deletedByName || item.deletedBy}
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {formatDate(item.deletedAt)}
                          </div>
                          <div className="col-span-1 text-sm text-gray-600">
                            {item.itemType === DeletedItemType.File ? formatFileSize(item.fileSize) : '--'}
                          </div>
                          <div className="col-span-2">
                            <div className="flex items-center gap-1">
                              <button
                                onClick={() => handleRestore([item.id])}
                                disabled={!item.canRestore}
                                className="p-1 text-gray-400 hover:text-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                title={item.canRestore ? "Khôi phục" : "Không thể khôi phục"}
                              >
                                <ArrowPathIcon className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handlePermanentDelete([item.id])}
                                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                title="Xóa vĩnh viễn"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Empty State */}
                    {!state.loading && state.deletedItems.length === 0 && (
                      <div className="px-6 py-12 text-center animate-fade-in">
                        <TrashIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          Thùng rác trống
                        </h3>
                        <p className="text-gray-500">
                          Không có files hoặc folders nào đã bị xóa.
                        </p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </>
          ) : (
            /* Grid View */
            <div className="p-6">
              {state.loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-600">Đang tải...</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {/* Deleted Items in Grid */}
                  {state.deletedItems.map((item, index) => (
                    <div 
                      key={item.id} 
                      className="card p-4 hover:shadow-md transition-all animate-fade-in"
                      style={{ animationDelay: `${index * 0.05}s` }}
                    >
                      <div className="text-center">
                        {item.itemType === DeletedItemType.Folder ? (
                          <FolderIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                        ) : (
                          <DocumentIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                        )}
                        <p className="text-sm font-medium text-gray-700 truncate" title={item.name}>
                          {item.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {item.itemType === DeletedItemType.File ? 
                            formatFileSize(item.fileSize) : 
                            'Folder'
                          }
                        </p>
                      </div>
                      <div className="mt-2 flex justify-center">
                        <input
                          type="checkbox"
                          checked={state.selectedItems.has(item.id)}
                          onChange={() => toggleItemSelection(item.id)}
                          className="rounded border-gray-300"
                        />
                      </div>
                    </div>
                  ))}

                  {/* Empty State for Grid */}
                  {!state.loading && state.deletedItems.length === 0 && (
                    <div className="col-span-full text-center py-12 animate-fade-in">
                      <TrashIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Thùng rác trống
                      </h3>
                      <p className="text-gray-500">
                        Không có files hoặc folders nào đã bị xóa.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Pagination */}
        {state.pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 animate-fade-in">
            <div className="text-sm text-gray-700">
              Hiển thị {((state.pagination.page - 1) * state.pagination.pageSize) + 1} đến{' '}
              {Math.min(state.pagination.page * state.pagination.pageSize, state.pagination.totalItems)} của{' '}
              {state.pagination.totalItems} kết quả
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => handlePageChange(state.pagination.page - 1)}
                disabled={state.pagination.page === 1}
                className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeftIcon className="w-4 h-4" />
              </button>
              
              {/* Page numbers */}
              {Array.from({ length: Math.min(5, state.pagination.totalPages) }, (_, i) => {
                const pageNumber = Math.max(1, state.pagination.page - 2) + i;
                if (pageNumber <= state.pagination.totalPages) {
                  return (
                    <button
                      key={pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        pageNumber === state.pagination.page
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                      }`}
                    >
                      {pageNumber}
                    </button>
                  );
                }
                return null;
              })}

              <button
                onClick={() => handlePageChange(state.pagination.page + 1)}
                disabled={state.pagination.page === state.pagination.totalPages}
                className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRightIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 