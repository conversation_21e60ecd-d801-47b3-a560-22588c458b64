"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  FolderIcon,
  DocumentIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  PencilIcon,
  TrashIcon,
  ShareIcon,
  EyeIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  Bars3Icon,
  ViewColumnsIcon,
  CalendarDaysIcon,
  UserIcon,
  TagIcon,
  EllipsisVerticalIcon,
} from "@heroicons/react/24/outline";
import { FolderService } from "@/api/services/folderService";
import { FileService } from "@/api/services/fileService";
import { ApiClient } from "@/api/core/apiClient";
import {
  FolderDto,
  FileDto,
  SortField,
  SortDirection,
  FolderContentsResponse
} from "@/api/types/interfaces";

interface EnhancedFolderManagerProps {
  apiClient: ApiClient;
  initialFolderId?: string;
  onFileSelect?: (file: FileDto) => void;
  onFolderSelect?: (folder: FolderDto) => void;
}

interface FolderManagerState {
  currentFolder: FolderDto | null;
  breadcrumb: FolderDto[];
  folders: FolderDto[];
  files: FileDto[];
  selectedItems: Set<string>;
  loading: boolean;
  error: string | null;
  searchTerm: string;
  filters: {
    folderType: 'all' | 'public' | 'private' | 'shared';
    uploaderEmail: string;
    dateRange: {
      from: string;
      to: string;
    };
  };
  sorting: {
    field: SortField;
    direction: SortDirection;
  };
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  };
  viewMode: 'list' | 'grid';
}

export function EnhancedFolderManager({
  apiClient,
  initialFolderId,
  onFileSelect,
  onFolderSelect
}: EnhancedFolderManagerProps) {
  const [state, setState] = useState<FolderManagerState>({
    currentFolder: null,
    breadcrumb: [],
    folders: [],
    files: [],
    selectedItems: new Set(),
    loading: false,
    error: null,
    searchTerm: "",
    filters: {
      folderType: 'all',
      uploaderEmail: '',
      dateRange: { from: '', to: '' }
    },
    sorting: {
      field: SortField.CreatedAt,
      direction: SortDirection.DESC
    },
    pagination: {
      page: 1,
      pageSize: 20,
      totalItems: 0,
      totalPages: 0
    },
    viewMode: 'list'
  });

  const folderService = new FolderService(apiClient);
  const fileService = new FileService(apiClient);

  // Load folder contents
  const loadFolderContents = useCallback(async (folderId?: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      let folderContents: FolderContentsResponse;
      let currentFolder: FolderDto | null = null;
      let breadcrumb: FolderDto[] = [];

      if (folderId) {
        // Get folder details and contents
        currentFolder = await folderService.getById(folderId);
        breadcrumb = await folderService.getPath(folderId);

        folderContents = await folderService.getContents(folderId, {
          page: state.pagination.page,
          pageSize: state.pagination.pageSize,
          sortBy: state.sorting.field,
          sortDirection: state.sorting.direction
        });
      } else {
        // Get root folders
        const response = await folderService.list({
          page: state.pagination.page,
          pageSize: state.pagination.pageSize,
          sortBy: state.sorting.field,
          sortDirection: state.sorting.direction,
          parentFolderId: undefined // Root level
        });

        folderContents = {
          folders: response.folders,
          files: [],
          pagination: response.pagination
        };
      }

      setState(prev => ({
        ...prev,
        currentFolder,
        breadcrumb,
        folders: folderContents.folders,
        files: folderContents.files,
        pagination: {
          ...prev.pagination,
          totalItems: folderContents.pagination.totalItems,
          totalPages: folderContents.pagination.totalPages
        },
        loading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load folder contents',
        loading: false
      }));
    }
  }, [folderService, state.pagination.page, state.pagination.pageSize, state.sorting]);

  // Initialize component
  useEffect(() => {
    loadFolderContents(initialFolderId);
  }, [loadFolderContents, initialFolderId]);

  // Handle folder navigation
  const navigateToFolder = (folderId: string) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page: 1 }
    }));
    loadFolderContents(folderId);
  };

  // Handle breadcrumb navigation
  const navigateToBreadcrumb = (index: number) => {
    if (index === 0) {
      loadFolderContents(); // Navigate to root
    } else {
      navigateToFolder(state.breadcrumb[index].id);
    }
  };

  // Handle search
  const handleSearch = (searchTerm: string) => {
    setState(prev => ({ ...prev, searchTerm }));
    // TODO: Implement search API call
  };

  // Handle sorting
  const handleSort = (field: SortField) => {
    setState(prev => ({
      ...prev,
      sorting: {
        field,
        direction: prev.sorting.field === field && prev.sorting.direction === SortDirection.ASC
          ? SortDirection.DESC
          : SortDirection.ASC
      }
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page }
    }));
  };

  // Handle item selection
  const toggleItemSelection = (itemId: string) => {
    setState(prev => {
      const newSelection = new Set(prev.selectedItems);
      if (newSelection.has(itemId)) {
        newSelection.delete(itemId);
      } else {
        newSelection.add(itemId);
      }
      return { ...prev, selectedItems: newSelection };
    });
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  };

  // Render breadcrumb
  const renderBreadcrumb = () => (
    <nav className="flex mb-4" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        <li>
          <button
            onClick={() => navigateToBreadcrumb(0)}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            Root
          </button>
        </li>
        {state.breadcrumb.map((folder, index) => (
          <li key={folder.id} className="flex items-center">
            <ChevronRightIcon className="w-4 h-4 text-gray-400 mx-1" />
            <button
              onClick={() => navigateToBreadcrumb(index)}
              className={`font-medium ${
                index === state.breadcrumb.length - 1
                  ? 'text-gray-900'
                  : 'text-blue-600 hover:text-blue-800'
              }`}
            >
              {folder.name}
            </button>
          </li>
        ))}
      </ol>
    </nav>
  );

  // Render filters and search
  const renderFiltersAndSearch = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="flex-1">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Tìm kiếm theo Tên Folder/ File"
              value={state.searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Filters */}
        <div className="flex gap-3">
          {/* Folder Type Filter */}
          <select
            value={state.filters.folderType}
            onChange={(e) => setState(prev => ({
              ...prev,
              filters: { ...prev.filters, folderType: e.target.value as any }
            }))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">Loại</option>
            <option value="public">Public</option>
            <option value="private">Private</option>
            <option value="shared">Shared</option>
          </select>

          {/* Uploader Filter */}
          <input
            type="text"
            placeholder="Người upload"
            value={state.filters.uploaderEmail}
            onChange={(e) => setState(prev => ({
              ...prev,
              filters: { ...prev.filters, uploaderEmail: e.target.value }
            }))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[200px]"
          />

          {/* Search Button */}
          <button className="px-6 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors">
            Tìm kiếm
          </button>
        </div>
      </div>
    </div>
  );

  // Render toolbar
  const renderToolbar = () => (
    <div className="flex justify-between items-center mb-4">
      <div className="flex items-center gap-3">
        <button
          onClick={() => {/* Handle new folder */}}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlusIcon className="w-4 h-4" />
          Thêm mới
        </button>

        {state.selectedItems.size > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              {state.selectedItems.size} selected
            </span>
            <button className="p-2 text-gray-600 hover:text-red-600">
              <TrashIcon className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      <div className="flex items-center gap-2">
        <button
          onClick={() => setState(prev => ({
            ...prev,
            viewMode: prev.viewMode === 'list' ? 'grid' : 'list'
          }))}
          className="p-2 text-gray-600 hover:text-gray-800"
        >
          {state.viewMode === 'list' ? <ViewColumnsIcon className="w-4 h-4" /> : <Bars3Icon className="w-4 h-4" />}
        </button>
      </div>
    </div>
  );

  // Render items list
  const renderItemsList = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Table Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
          <div className="col-span-1">
            <input
              type="checkbox"
              className="rounded border-gray-300"
              onChange={(e) => {
                // Handle select all
              }}
            />
          </div>
          <div className="col-span-1">Loại</div>
          <div className="col-span-3">
            <button
              onClick={() => handleSort(SortField.Name)}
              className="flex items-center gap-1 hover:text-gray-900"
            >
              Tên
              {state.sorting.field === SortField.Name && (
                <span className="text-xs">
                  {state.sorting.direction === SortDirection.ASC ? '↑' : '↓'}
                </span>
              )}
            </button>
          </div>
          <div className="col-span-2">Người upload</div>
          <div className="col-span-2">
            <button
              onClick={() => handleSort(SortField.CreatedAt)}
              className="flex items-center gap-1 hover:text-gray-900"
            >
              Sửa đổi lần cuối
              {state.sorting.field === SortField.CreatedAt && (
                <span className="text-xs">
                  {state.sorting.direction === SortDirection.ASC ? '↑' : '↓'}
                </span>
              )}
            </button>
          </div>
          <div className="col-span-1">Kích cỡ tập</div>
          <div className="col-span-2">Thao tác</div>
        </div>
      </div>

      {/* Items */}
      <div className="divide-y divide-gray-200">
        {state.loading ? (
          <div className="px-6 py-12 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">Đang tải...</p>
          </div>
        ) : (
          <>
            {/* Folders */}
            {state.folders.map((folder) => (
              <div key={folder.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="grid grid-cols-12 gap-4 items-center">
                  <div className="col-span-1">
                    <input
                      type="checkbox"
                      checked={state.selectedItems.has(folder.id)}
                      onChange={() => toggleItemSelection(folder.id)}
                      className="rounded border-gray-300"
                    />
                  </div>
                  <div className="col-span-1">
                    <FolderIcon className="w-6 h-6 text-blue-500" />
                  </div>
                  <div className="col-span-3">
                    <button
                      onClick={() => navigateToFolder(folder.id)}
                      className="font-medium text-blue-600 hover:text-blue-800"
                    >
                      {folder.name}
                    </button>
                    {folder.description && (
                      <p className="text-sm text-gray-500 mt-1">{folder.description}</p>
                    )}
                  </div>
                  <div className="col-span-2 text-sm text-gray-600">
                    {folder.ownerId}
                  </div>
                  <div className="col-span-2 text-sm text-gray-600">
                    {formatDate(folder.updatedAt)}
                  </div>
                  <div className="col-span-1 text-sm text-gray-600">
                    --
                  </div>
                  <div className="col-span-2">
                    <div className="flex items-center gap-1">
                      <button
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title="Download"
                      >
                        <ArrowDownTrayIcon className="w-4 h-4" />
                      </button>
                      <button
                        className="p-1 text-gray-400 hover:text-yellow-600"
                        title="Edit"
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                      <button
                        className="p-1 text-gray-400 hover:text-green-600"
                        title="Share"
                      >
                        <ShareIcon className="w-4 h-4" />
                      </button>
                      <button
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="Delete"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Files */}
            {state.files.map((file) => (
              <div key={file.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="grid grid-cols-12 gap-4 items-center">
                  <div className="col-span-1">
                    <input
                      type="checkbox"
                      checked={state.selectedItems.has(file.id)}
                      onChange={() => toggleItemSelection(file.id)}
                      className="rounded border-gray-300"
                    />
                  </div>
                  <div className="col-span-1">
                    <DocumentIcon className="w-6 h-6 text-red-500" />
                  </div>
                  <div className="col-span-3">
                    <button
                      onClick={() => onFileSelect?.(file)}
                      className="font-medium text-gray-900 hover:text-blue-600"
                    >
                      {file.displayName || file.name}
                    </button>
                    {file.description && (
                      <p className="text-sm text-gray-500 mt-1">{file.description}</p>
                    )}
                  </div>
                  <div className="col-span-2 text-sm text-gray-600">
                    {file.ownerId}
                  </div>
                  <div className="col-span-2 text-sm text-gray-600">
                    {formatDate(file.updatedAt)}
                  </div>
                  <div className="col-span-1 text-sm text-gray-600">
                    {formatFileSize(file.fileSize)}
                  </div>
                  <div className="col-span-2">
                    <div className="flex items-center gap-1">
                      <button
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title="Download"
                      >
                        <ArrowDownTrayIcon className="w-4 h-4" />
                      </button>
                      <button
                        className="p-1 text-gray-400 hover:text-yellow-600"
                        title="Edit"
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                      <button
                        className="p-1 text-gray-400 hover:text-green-600"
                        title="Share"
                      >
                        <ShareIcon className="w-4 h-4" />
                      </button>
                      <button
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="Delete"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
      </div>

      {/* Pagination */}
      {state.pagination.totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Showing {((state.pagination.page - 1) * state.pagination.pageSize) + 1} to{' '}
              {Math.min(state.pagination.page * state.pagination.pageSize, state.pagination.totalItems)} of{' '}
              {state.pagination.totalItems} results
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => handlePageChange(state.pagination.page - 1)}
                disabled={state.pagination.page === 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>

              {Array.from({ length: Math.min(5, state.pagination.totalPages) }, (_, i) => {
                const pageNum = i + 1;
                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`px-3 py-1 text-sm border rounded ${
                      pageNum === state.pagination.page
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}

              <button
                onClick={() => handlePageChange(state.pagination.page + 1)}
                disabled={state.pagination.page === state.pagination.totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  if (state.error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-800">{state.error}</p>
        <button
          onClick={() => loadFolderContents(state.currentFolder?.id)}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Quản lý folder</h1>
          {renderBreadcrumb()}
        </div>
      </div>

      {renderFiltersAndSearch()}
      {renderToolbar()}
      {renderItemsList()}
    </div>
  );
}
