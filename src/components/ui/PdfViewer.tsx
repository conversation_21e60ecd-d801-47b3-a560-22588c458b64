"use client";

import React, { useRef, useEffect, useState } from "react";
import { useVirtualizer } from "@tanstack/react-virtual";
import { usePdfRender } from "@/hooks/usePdfRender";
import { useViewerStore } from "@/store/viewer-store";
import { PdfViewerProps } from "@/types";

export function PdfViewer({
  fileUrl,
  onPageChange,
  onScaleChange,
  onTotalPagesChange,
  initialPage = 1,
  initialScale = 1.0,
}: PdfViewerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerHeight, setContainerHeight] = useState(600);

  const { currentPage, scale, rotation, setCurrentPage, setScale } =
    useViewerStore();

  const {
    isLoading,
    error,
    numPages,
    renderPage,
    getPageDimensions,
    loadDocument,
  } = usePdfRender({ fileUrl, autoLoad: true });

  // Initialize viewer state
  useEffect(() => {
    setCurrentPage(initialPage);
    setScale(initialScale);
  }, [initialPage, initialScale, setCurrentPage, setScale]);

  // Update container height
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight);
      }
    };

    updateHeight();
    window.addEventListener("resize", updateHeight);
    return () => window.removeEventListener("resize", updateHeight);
  }, []);

  // Notify parent of page changes
  useEffect(() => {
    onPageChange?.(currentPage);
  }, [currentPage, onPageChange]);

  // Notify parent of scale changes
  useEffect(() => {
    onScaleChange?.(scale);
  }, [scale, onScaleChange]);

  // Notify parent of total pages
  useEffect(() => {
    if (numPages > 0) {
      onTotalPagesChange?.(numPages);
    }
  }, [numPages, onTotalPagesChange]);

  // Virtual scrolling setup
  const virtualizer = useVirtualizer({
    count: numPages,
    getScrollElement: () => containerRef.current,
    estimateSize: () => 800 * scale + 20, // Estimated page height + margin
    overscan: 2,
  });

  // Page component
  const PageComponent = ({ index }: { index: number }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [pageLoading, setPageLoading] = useState(true);
    const [pageError, setPageError] = useState<string | null>(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

    const pageNumber = index + 1;

    useEffect(() => {
      const renderCurrentPage = async () => {
        if (!canvasRef.current) return;

        setPageLoading(true);
        setPageError(null);

        try {
          // Get page dimensions first
          const dims = await getPageDimensions(pageNumber, scale);
          setDimensions(dims);

          // Render page
          await renderPage(pageNumber, canvasRef.current, { scale, rotation });
        } catch (err) {
          const errorMessage =
            err instanceof Error ? err.message : "Failed to render page";
          setPageError(errorMessage);
        } finally {
          setPageLoading(false);
        }
      };

      renderCurrentPage();
    }, [pageNumber, scale, rotation, renderPage, getPageDimensions]);

    return (
      <div className="flex flex-col items-center p-2 bg-white shadow-sm border border-gray-200 rounded-lg">
        <div className="mb-2 text-sm text-gray-600">
          Page {pageNumber} of {numPages}
        </div>

        {pageLoading && (
          <div className="flex items-center justify-center w-full h-96 bg-gray-100 rounded">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        )}

        {pageError && (
          <div className="flex items-center justify-center w-full h-96 bg-red-50 rounded border border-red-200">
            <div className="text-center">
              <div className="text-red-600 font-medium">Error loading page</div>
              <div className="text-red-500 text-sm mt-1">{pageError}</div>
            </div>
          </div>
        )}

        <canvas
          ref={canvasRef}
          className={`max-w-full h-auto border border-gray-300 rounded ${
            pageLoading || pageError ? "hidden" : "block"
          }`}
          style={{
            width: dimensions.width > 0 ? `${dimensions.width}px` : "auto",
            height: dimensions.height > 0 ? `${dimensions.height}px` : "auto",
          }}
        />
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <div className="mt-4 text-gray-600">Loading PDF...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full bg-red-50">
        <div className="text-center">
          <div className="text-red-600 font-medium text-lg">
            Error loading PDF
          </div>
          <div className="text-red-500 mt-2">{error}</div>
          <button
            onClick={() => fileUrl && loadDocument(fileUrl)}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (numPages === 0) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-50">
        <div className="text-center text-gray-600">No PDF loaded</div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="h-full overflow-auto bg-gray-100 p-4"
      style={{ height: containerHeight }}
    >
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: "100%",
          position: "relative",
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => (
          <div
            key={virtualItem.key}
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`,
            }}
          >
            <PageComponent index={virtualItem.index} />
          </div>
        ))}
      </div>
    </div>
  );
}
