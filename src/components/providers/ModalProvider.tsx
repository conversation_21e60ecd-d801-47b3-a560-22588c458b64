"use client";

import React, { createContext, useContext, useState } from "react";
import { UserFormModal } from "@/components/modals/UserFormModal";
import { DeleteConfirmModal } from "@/components/modals/DeleteConfirmModal";
import { PermissionFormModal } from "@/components/modals/PermissionFormModal";
import { RolePermissionModal } from "@/components/modals/RolePermissionModal";
import { FolderManagementModal } from "@/components/modals/FolderManagementModal";

interface User {
  id?: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  username: string;
  role: string;
  department: string;
  isActive: boolean;
}

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  actions: string[];
  isActive?: boolean;
}

interface Role {
  id?: string;
  name: string;
  description: string;
  permissions: string[];
  color: string;
  isActive: boolean;
  userCount?: number;
}

interface ModalContextType {
  // User modals
  openAddUserModal: () => void;
  openEditUserModal: (user: User) => void;
  openViewUserModal: (user: User) => void;

  // Permission modals
  openAddPermissionModal: () => void;
  openEditPermissionModal: (permission: Permission) => void;
  openViewPermissionModal: (permission: Permission) => void;

  // Role modals
  openAddRoleModal: (availablePermissions: Permission[]) => void;
  openEditRoleModal: (role: Role, availablePermissions: Permission[]) => void;
  openViewRoleModal: (role: Role, availablePermissions: Permission[]) => void;

  // Folder management modals
  openCreateFolderModal: (onCreateFolder: (name: string) => void) => void;
  openUploadFilesModal: (onUploadFiles: (files: File[]) => void) => void;

  // Delete modal
  openDeleteModal: (config: {
    title?: string;
    message?: string;
    itemName?: string;
    itemType?: string;
    onConfirm: () => void;
  }) => void;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

export function useModal() {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error("useModal must be used within a ModalProvider");
  }
  return context;
}

interface ModalProviderProps {
  children: React.ReactNode;
}

export function ModalProvider({ children }: ModalProviderProps) {
  // User modal states
  const [userModalOpen, setUserModalOpen] = useState(false);
  const [userModalMode, setUserModalMode] = useState<"add" | "edit" | "view">(
    "add"
  );
  const [selectedUser, setSelectedUser] = useState<User | undefined>();

  // Permission modal states
  const [permissionModalOpen, setPermissionModalOpen] = useState(false);
  const [permissionModalMode, setPermissionModalMode] = useState<
    "add" | "edit" | "view"
  >("add");
  const [selectedPermission, setSelectedPermission] = useState<
    Permission | undefined
  >();

  // Role modal states
  const [roleModalOpen, setRoleModalOpen] = useState(false);
  const [roleModalMode, setRoleModalMode] = useState<"add" | "edit" | "view">(
    "add"
  );
  const [selectedRole, setSelectedRole] = useState<Role | undefined>();
  const [availablePermissions, setAvailablePermissions] = useState<
    Permission[]
  >([]);

  // Folder management modal states
  const [folderModalOpen, setFolderModalOpen] = useState(false);
  const [folderModalMode, setFolderModalMode] = useState<
    "createFolder" | "uploadFiles"
  >("createFolder");
  const [folderCallback, setFolderCallback] = useState<{
    onCreateFolder?: (name: string) => void;
    onUploadFiles?: (files: File[]) => void;
  }>({});

  // Delete modal states
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deleteConfig, setDeleteConfig] = useState<{
    title?: string;
    message?: string;
    itemName?: string;
    itemType?: string;
    onConfirm: () => void;
  }>({
    onConfirm: () => {},
  });
  const [isDeleting, setIsDeleting] = useState(false);

  const contextValue: ModalContextType = {
    openAddUserModal: () => {
      setUserModalMode("add");
      setSelectedUser(undefined);
      setUserModalOpen(true);
    },

    openEditUserModal: (user: User) => {
      setUserModalMode("edit");
      setSelectedUser(user);
      setUserModalOpen(true);
    },

    openViewUserModal: (user: User) => {
      setUserModalMode("view");
      setSelectedUser(user);
      setUserModalOpen(true);
    },

    openAddPermissionModal: () => {
      setPermissionModalMode("add");
      setSelectedPermission(undefined);
      setPermissionModalOpen(true);
    },

    openEditPermissionModal: (permission: Permission) => {
      setPermissionModalMode("edit");
      setSelectedPermission(permission);
      setPermissionModalOpen(true);
    },

    openViewPermissionModal: (permission: Permission) => {
      setPermissionModalMode("view");
      setSelectedPermission(permission);
      setPermissionModalOpen(true);
    },

    openAddRoleModal: (permissions: Permission[]) => {
      setRoleModalMode("add");
      setSelectedRole(undefined);
      setAvailablePermissions(permissions);
      setRoleModalOpen(true);
    },

    openEditRoleModal: (role: Role, permissions: Permission[]) => {
      setRoleModalMode("edit");
      setSelectedRole(role);
      setAvailablePermissions(permissions);
      setRoleModalOpen(true);
    },

    openViewRoleModal: (role: Role, permissions: Permission[]) => {
      setRoleModalMode("view");
      setSelectedRole(role);
      setAvailablePermissions(permissions);
      setRoleModalOpen(true);
    },

    openCreateFolderModal: (onCreateFolder: (name: string) => void) => {
      setFolderModalMode("createFolder");
      setFolderCallback({ onCreateFolder });
      setFolderModalOpen(true);
    },

    openUploadFilesModal: (onUploadFiles: (files: File[]) => void) => {
      setFolderModalMode("uploadFiles");
      setFolderCallback({ onUploadFiles });
      setFolderModalOpen(true);
    },

    openDeleteModal: (config) => {
      setDeleteConfig(config);
      setDeleteModalOpen(true);
    },
  };

  const handleUserSubmit = (user: User) => {
    console.log("User submitted:", user);
    // Here you would typically call an API
    // For now, just close the modal
    setUserModalOpen(false);
  };

  const handlePermissionSubmit = (permission: Permission) => {
    console.log("Permission submitted:", permission);
    // Here you would typically call an API
    // For now, just close the modal
    setPermissionModalOpen(false);
  };

  const handleRoleSubmit = (role: Role) => {
    console.log("Role submitted:", role);
    // Here you would typically call an API
    // For now, just close the modal
    setRoleModalOpen(false);
  };

  const handleFolderCreate = (name: string) => {
    if (folderCallback.onCreateFolder) {
      folderCallback.onCreateFolder(name);
    }
    setFolderModalOpen(false);
  };

  const handleFilesUpload = (files: File[]) => {
    if (folderCallback.onUploadFiles) {
      folderCallback.onUploadFiles(files);
    }
    setFolderModalOpen(false);
  };

  const handleDeleteConfirm = async () => {
    setIsDeleting(true);
    try {
      await deleteConfig.onConfirm();
      setDeleteModalOpen(false);
    } catch (error) {
      console.error("Delete failed:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <ModalContext.Provider value={contextValue}>
      {children}

      {/* User Form Modal */}
      <UserFormModal
        isOpen={userModalOpen}
        onClose={() => setUserModalOpen(false)}
        mode={userModalMode}
        user={selectedUser}
        onSubmit={handleUserSubmit}
      />

      {/* Permission Form Modal */}
      <PermissionFormModal
        isOpen={permissionModalOpen}
        onClose={() => setPermissionModalOpen(false)}
        mode={permissionModalMode}
        permission={selectedPermission}
        onSubmit={handlePermissionSubmit}
      />

      {/* Role Permission Modal */}
      <RolePermissionModal
        isOpen={roleModalOpen}
        onClose={() => setRoleModalOpen(false)}
        mode={roleModalMode}
        role={selectedRole}
        availablePermissions={availablePermissions}
        onSubmit={handleRoleSubmit}
      />

      {/* Folder Management Modal */}
      <FolderManagementModal
        isOpen={folderModalOpen}
        onClose={() => setFolderModalOpen(false)}
        mode={folderModalMode}
        onCreateFolder={handleFolderCreate}
        onUploadFiles={handleFilesUpload}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title={deleteConfig.title}
        message={deleteConfig.message}
        itemName={deleteConfig.itemName}
        itemType={deleteConfig.itemType}
        isLoading={isDeleting}
      />
    </ModalContext.Provider>
  );
}
