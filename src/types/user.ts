/**
 * User information interfaces for the application
 */

export interface UserProfile {
  sub: string; // User ID from OIDC
  email: string;
  name: string;
  role: string;
  preferred_username: string;
  email_verified: boolean;
  // Optional extended fields (may not be in connect/userinfo response)
  firstName?: string;
  lastName?: string;
  avatar?: string;
  phone?: string;
  department?: string;
  position?: string;
  address?: string;
  bio?: string;
  joinDate?: string;
  lastLogin?: string;
  isActive?: boolean;
  roles?: string[];
  permissions?: string[];
  preferences?: UserPreferences;
}

export interface UserPreferences {
  language?: string;
  theme?: 'light' | 'dark' | 'auto';
  timezone?: string;
  notifications?: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}



export interface UpdateUserProfileRequest {
  name?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  department?: string;
  position?: string;
  address?: string;
  bio?: string;
  avatar?: string;
  preferences?: Partial<UserPreferences>;
}

export interface UpdateUserProfileResponse {
  success: boolean;
  data: UserProfile;
  message?: string;
}
