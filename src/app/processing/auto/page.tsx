"use client";

import React, { useState, useCallback, useEffect } from "react";
import {
  ClockIcon,
  DocumentIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowUpTrayIcon,
  ArrowDownTrayIcon,
  EyeIcon,
  TrashIcon,
  XMarkIcon,
  MagnifyingGlassPlusIcon,
  MagnifyingGlassMinusIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
} from "@heroicons/react/24/outline";
import { useDropzone } from "react-dropzone";

interface AutoSplitJob {
  id: string;
  fileName: string;
  originalSize: string;
  status: "uploading" | "processing" | "completed" | "failed";
  progress: number;
  uploadTime: Date;
  completedTime?: Date;
  resultFiles?: {
    name: string;
    size: string;
    downloadUrl: string;
  }[];
  error?: string;
}

export default function AutoDetachPage() {
  const [jobs, setJobs] = useState<AutoSplitJob[]>([
    {
      id: "1",
      fileName: "Invoice_Document.pdf",
      originalSize: "2.4 MB",
      status: "completed",
      progress: 100,
      uploadTime: new Date(Date.now() - 1800000),
      completedTime: new Date(Date.now() - 1200000),
      resultFiles: [
        {
          name: "Invoice_Document_page_1.pdf",
          size: "0.8 MB",
          downloadUrl: "/download/1",
        },
        {
          name: "Invoice_Document_page_2.pdf",
          size: "0.7 MB",
          downloadUrl: "/download/2",
        },
        {
          name: "Invoice_Document_page_3.pdf",
          size: "0.9 MB",
          downloadUrl: "/download/3",
        },
      ],
    },
  ]);

  const [isUploading, setIsUploading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [previewFile, setPreviewFile] = useState<{
    name: string;
    url: string;
  } | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [zoomLevel, setZoomLevel] = useState(100);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Helper function to format date consistently
  const formatDate = (date: Date) => {
    if (!isMounted) return ""; // Return empty string during SSR
    return (
      date.toLocaleDateString("vi-VN") +
      " " +
      date.toLocaleTimeString("vi-VN", {
        hour: "2-digit",
        minute: "2-digit",
      })
    );
  };

  // Upload files using dropzone
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setIsUploading(true);

    for (const file of acceptedFiles) {
      const newJob: AutoSplitJob = {
        id: Date.now().toString() + Math.random(),
        fileName: file.name,
        originalSize: `${(file.size / (1024 * 1024)).toFixed(1)} MB`,
        status: "uploading",
        progress: 0,
        uploadTime: new Date(),
      };

      setJobs((prev) => [newJob, ...prev]);

      // Simulate upload and processing
      await simulateAutoSplit(newJob.id, file);
    }

    setIsUploading(false);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
    },
    multiple: true,
  });

  // Simulate auto split process
  const simulateAutoSplit = async (jobId: string, file: File) => {
    // Update to processing
    setJobs((prev) =>
      prev.map((job) =>
        job.id === jobId ? { ...job, status: "processing" as const } : job
      )
    );

    // Simulate progress
    for (let progress = 0; progress <= 100; progress += 10) {
      await new Promise((resolve) => setTimeout(resolve, 200));
      setJobs((prev) =>
        prev.map((job) => (job.id === jobId ? { ...job, progress } : job))
      );
    }

    // Complete with mock result files
    const resultFiles = [
      {
        name: `${file.name.replace(".pdf", "")}_page_1.pdf`,
        size: "0.8 MB",
        downloadUrl: `/download/${jobId}_1`,
      },
      {
        name: `${file.name.replace(".pdf", "")}_page_2.pdf`,
        size: "0.7 MB",
        downloadUrl: `/download/${jobId}_2`,
      },
      {
        name: `${file.name.replace(".pdf", "")}_page_3.pdf`,
        size: "0.9 MB",
        downloadUrl: `/download/${jobId}_3`,
      },
    ];

    setJobs((prev) =>
      prev.map((job) =>
        job.id === jobId
          ? {
              ...job,
              status: "completed" as const,
              completedTime: new Date(),
              resultFiles,
            }
          : job
      )
    );
  };

  const getStatusIcon = (status: AutoSplitJob["status"]) => {
    switch (status) {
      case "uploading":
        return <ArrowUpTrayIcon className="h-5 w-5 text-blue-500" />;
      case "processing":
        return (
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
        );
      case "completed":
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case "failed":
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: AutoSplitJob["status"]) => {
    switch (status) {
      case "uploading":
        return "bg-blue-100 text-blue-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleDownload = (downloadUrl: string, fileName: string) => {
    console.log(`Downloading: ${fileName} from ${downloadUrl}`);
    alert(`Tải xuống: ${fileName}`);
  };

  const handleDeleteJob = (jobId: string) => {
    setJobs((prev) => prev.filter((job) => job.id !== jobId));
  };

  const handlePreview = (fileName: string, downloadUrl?: string) => {
    // For demo purposes, we'll use a sample PDF URL
    // In real app, this would be the actual file URL
    const pdfUrl = downloadUrl || "/.pdf";
    setPreviewFile({
      name: fileName,
      url: pdfUrl,
    });
  };

  const closePreview = () => {
    setPreviewFile(null);
    setIsFullscreen(false);
    setCurrentPage(1);
    setTotalPages(1);
    setZoomLevel(100);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const zoomIn = () => {
    setZoomLevel((prev) => Math.min(prev + 25, 200));
  };

  const zoomOut = () => {
    setZoomLevel((prev) => Math.max(prev - 25, 50));
  };

  const nextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const prevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  return (
    <div className="py-6 space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <ClockIcon className="h-8 w-8 text-blue-600 mr-3" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Auto Detach Processing
            </h1>
            <p className="text-gray-600">
              Tải PDF lên và tự động tách thành từng trang
            </p>
          </div>
        </div>
      </div>

      {/* Upload Zone */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Tải PDF lên để tự động tách
        </h2>
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragActive
              ? "border-blue-500 bg-blue-50"
              : "border-gray-300 hover:border-gray-400"
          } ${isUploading ? "opacity-50 cursor-not-allowed" : ""}`}
        >
          <input {...getInputProps()} disabled={isUploading} />
          <ArrowUpTrayIcon className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-600">
            {isDragActive
              ? "Thả file PDF vào đây..."
              : "Kéo thả file PDF hoặc click để chọn"}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Hỗ trợ file PDF, tối đa 50MB
          </p>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ArrowUpTrayIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Đang xử lý</p>
              <p className="text-2xl font-semibold text-gray-900">
                {
                  jobs.filter(
                    (job) =>
                      job.status === "processing" || job.status === "uploading"
                  ).length
                }
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Hoàn thành</p>
              <p className="text-2xl font-semibold text-gray-900">
                {jobs.filter((job) => job.status === "completed").length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DocumentIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">File đã tách</p>
              <p className="text-2xl font-semibold text-gray-900">
                {jobs.reduce(
                  (sum, job) => sum + (job.resultFiles?.length || 0),
                  0
                )}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Jobs List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Lịch sử xử lý</h2>
        </div>
        <div className="divide-y divide-gray-200">
          {jobs.map((job) => (
            <div key={job.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {getStatusIcon(job.status)}
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">
                      {job.fileName}
                    </h3>
                    <div className="flex items-center space-x-4 mt-1">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                          job.status
                        )}`}
                      >
                        {job.status === "uploading"
                          ? "Đang tải lên"
                          : job.status === "processing"
                          ? "Đang xử lý"
                          : job.status === "completed"
                          ? "Hoàn thành"
                          : "Lỗi"}
                      </span>
                      <span className="text-sm text-gray-500">
                        {job.originalSize}
                      </span>
                      <span className="text-sm text-gray-500">
                        {formatDate(job.uploadTime)}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {job.status === "completed" && (
                    <button
                      onClick={() => handleDeleteJob(job.id)}
                      className="p-2 text-gray-400 hover:text-red-600"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  )}
                </div>
              </div>

              {(job.status === "uploading" || job.status === "processing") && (
                <div className="mt-4">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Tiến độ</span>
                    <span>{job.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${job.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {job.status === "completed" && job.resultFiles && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    File đã tách:
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                    {job.resultFiles.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded"
                      >
                        <div className="flex items-center space-x-2">
                          <DocumentIcon className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-900 truncate">
                            {file.name}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <button
                            onClick={() =>
                              handleDownload(file.downloadUrl, file.name)
                            }
                            className="p-1 text-gray-400 hover:text-blue-600"
                          >
                            <ArrowDownTrayIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() =>
                              handlePreview(file.name, file.downloadUrl)
                            }
                            className="p-1 text-gray-400 hover:text-gray-600"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Enhanced PDF Preview Modal */}
      {previewFile && (
        <div
          className={`fixed inset-0 z-50 ${
            isFullscreen ? "bg-black" : "bg-gray-900 bg-opacity-50"
          }`}
        >
          <div
            className={`${
              isFullscreen
                ? "h-full"
                : "flex items-center justify-center min-h-screen p-4"
            }`}
          >
            {!isFullscreen && (
              <div
                className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                onClick={closePreview}
              ></div>
            )}

            <div
              className={`${
                isFullscreen
                  ? "w-full h-full bg-white"
                  : "relative bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
              }`}
            >
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <DocumentIcon className="h-6 w-6 text-white" />
                    <div>
                      <h3 className="text-lg font-semibold text-white truncate max-w-md">
                        {previewFile.name}
                      </h3>
                      <p className="text-blue-100 text-sm">
                        PDF Document Preview
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* Zoom Controls */}
                    <div className="flex items-center space-x-1 bg-white bg-opacity-20 rounded-lg px-3 py-1">
                      <button
                        onClick={zoomOut}
                        className="p-1 text-white hover:bg-white hover:bg-opacity-20 rounded"
                        disabled={zoomLevel <= 50}
                      >
                        <MagnifyingGlassMinusIcon className="h-4 w-4" />
                      </button>
                      <span className="text-white text-sm font-medium min-w-[3rem] text-center">
                        {zoomLevel}%
                      </span>
                      <button
                        onClick={zoomIn}
                        className="p-1 text-white hover:bg-white hover:bg-opacity-20 rounded"
                        disabled={zoomLevel >= 200}
                      >
                        <MagnifyingGlassPlusIcon className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Page Navigation */}
                    <div className="flex items-center space-x-1 bg-white bg-opacity-20 rounded-lg px-3 py-1">
                      <button
                        onClick={prevPage}
                        className="p-1 text-white hover:bg-white hover:bg-opacity-20 rounded"
                        disabled={currentPage <= 1}
                      >
                        ←
                      </button>
                      <span className="text-white text-sm font-medium min-w-[3rem] text-center">
                        {currentPage}/{totalPages}
                      </span>
                      <button
                        onClick={nextPage}
                        className="p-1 text-white hover:bg-white hover:bg-opacity-20 rounded"
                        disabled={currentPage >= totalPages}
                      >
                        →
                      </button>
                    </div>

                    {/* Fullscreen Toggle */}
                    <button
                      onClick={toggleFullscreen}
                      className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                    >
                      {isFullscreen ? (
                        <ArrowsPointingInIcon className="h-5 w-5" />
                      ) : (
                        <ArrowsPointingOutIcon className="h-5 w-5" />
                      )}
                    </button>

                    {/* Close Button */}
                    <button
                      onClick={closePreview}
                      className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* PDF Viewer */}
              <div
                className={`${
                  isFullscreen ? "h-[calc(100vh-80px)]" : "h-[70vh]"
                } bg-gray-100`}
              >
                <iframe
                  src={`${previewFile.url}#toolbar=0&navpanes=0&scrollbar=1&page=${currentPage}&zoom=${zoomLevel}`}
                  className="w-full h-full border-0"
                  title={`Preview of ${previewFile.name}`}
                />
              </div>

              {/* Footer Actions */}
              {!isFullscreen && (
                <div className="bg-gray-50 px-6 py-4 border-t">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>Zoom: {zoomLevel}%</span>
                      <span>
                        Page: {currentPage} of {totalPages}
                      </span>
                    </div>

                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() =>
                          handleDownload(previewFile.url, previewFile.name)
                        }
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                        Download
                      </button>
                      <button
                        onClick={closePreview}
                        className="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                      >
                        Close
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
