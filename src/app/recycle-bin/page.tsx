"use client";

import React from 'react';
import { ApiClient } from '@/api/core/apiClient';
import { useAuth } from '@/contexts/AuthContext';
import { RecycleBinManager } from '@/components/folder-manager/RecycleBinManager';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

export default function RecycleBinPage() {
  const { user } = useAuth();
  const apiClient = new ApiClient(process.env.NEXT_PUBLIC_API_BASE_URL || 'https://localhost:7040');

  return (
    <ProtectedRoute>
      <RecycleBinManager apiClient={apiClient} />
    </ProtectedRoute>
  );
} 