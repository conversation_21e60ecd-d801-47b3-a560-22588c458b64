"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { 
  FolderIcon, 
  DocumentIcon, 
  PlusIcon, 
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  PencilIcon,
  TrashIcon,
  ShareIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  Bars3Icon,
  ViewColumnsIcon,
  UserIcon,
  CalendarDaysIcon,
  EllipsisVerticalIcon,
  FolderPlusIcon,
  CloudArrowUpIcon,
  ArchiveBoxIcon
} from '@heroicons/react/24/outline';
import { FolderService } from '@/api/services/folderService';
import { FileService } from '@/api/services/fileService';
import { GoogleDriveService } from '@/api/services/googleDriveService';
import { ApiClient } from '@/api/core/apiClient';
import { useAuth } from '@/contexts/AuthContext';
import {
  FolderDto,
  FileDto,
  SortField,
  SortDirection,
  FolderContentsResponse,
  FolderCreateData
} from '@/api/types/interfaces';

interface FolderManagerState {
  currentFolder: FolderDto | null;
  breadcrumb: FolderDto[];
  folders: FolderDto[];
  files: FileDto[];
  selectedItems: Set<string>;
  loading: boolean;
  error: string | null;
  searchTerm: string;
  filters: {
    folderType: 'all' | 'public' | 'private' | 'shared';
    uploaderEmail: string;
    dateRange: {
      from: string;
      to: string;
    };
  };
  sorting: {
    field: SortField;
    direction: SortDirection;
  };
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  };
  viewMode: 'list' | 'grid';
  showNewFolderModal: boolean;
  showBulkActions: boolean;
}

export default function FolderManagerPage() {
  const { user } = useAuth();
  
  // Fix API client initialization - use access_token directly from user
  const apiClient = new ApiClient(
    process.env.NEXT_PUBLIC_API_BASE_URL || 'https://localhost:7040',
    user?.access_token || ''
  );
  
  const [state, setState] = useState<FolderManagerState>({
    currentFolder: null,
    breadcrumb: [],
    folders: [],
    files: [],
    selectedItems: new Set(),
    loading: false,
    error: null,
    searchTerm: "",
    filters: {
      folderType: 'all',
      uploaderEmail: '',
      dateRange: { from: '', to: '' }
    },
    sorting: {
      field: SortField.CreatedAt,
      direction: SortDirection.DESC
    },
    pagination: {
      page: 1,
      pageSize: 20,
      totalItems: 0,
      totalPages: 0
    },
    viewMode: 'list',
    showNewFolderModal: false,
    showBulkActions: false
  });

  const folderService = new FolderService(apiClient);
  const fileService = new FileService(apiClient);
  const googleDriveService = new GoogleDriveService(apiClient);

  // Load folder contents
  const loadFolderContents = useCallback(async (folderId?: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      let folderContents: FolderContentsResponse;
      let currentFolder: FolderDto | null = null;
      let breadcrumb: FolderDto[] = [];

      if (folderId) {
        // Get folder details and contents
        currentFolder = await folderService.getById(folderId);
        breadcrumb = await folderService.getPath(folderId);

        folderContents = await folderService.getContents(folderId, {
          page: state.pagination.page,
          pageSize: state.pagination.pageSize,
          sortBy: state.sorting.field,
          sortDirection: state.sorting.direction
        });
      } else {
        // Get root folders
        const response = await folderService.getList({
          page: state.pagination.page,
          pageSize: state.pagination.pageSize,
          sortBy: state.sorting.field,
          sortDirection: state.sorting.direction,
          parentFolderId: undefined,
          search: state.searchTerm || undefined,
          folderType: state.filters.folderType,
          uploaderEmail: state.filters.uploaderEmail || undefined
        });

        folderContents = {
          folders: response.items,
          files: [],
          pagination: response.pagination
        };
      }

      setState(prev => ({
        ...prev,
        currentFolder,
        breadcrumb,
        folders: folderContents.folders,
        files: folderContents.files,
        pagination: {
          ...prev.pagination,
          totalItems: folderContents.pagination.totalItems,
          totalPages: folderContents.pagination.totalPages
        },
        loading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load folder contents',
        loading: false
      }));
    }
  }, [folderService, state.pagination.page, state.pagination.pageSize, state.sorting, state.searchTerm, state.filters]);

  // Initialize component
  useEffect(() => {
    if (user?.access_token) {
      loadFolderContents();
    }
  }, [user?.access_token]);

  // Reload when dependencies change
  useEffect(() => {
    if (user?.access_token) {
      loadFolderContents(state.currentFolder?.id);
    }
  }, [state.pagination.page, state.pagination.pageSize, state.sorting]);

  // Handle folder navigation
  const navigateToFolder = (folderId: string) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page: 1 }
    }));
    loadFolderContents(folderId);
  };

  // Handle breadcrumb navigation
  const navigateToBreadcrumb = (index: number) => {
    if (index === 0) {
      loadFolderContents(); // Navigate to root
    } else {
      navigateToFolder(state.breadcrumb[index].id);
    }
  };

  // Handle search
  const handleSearch = useCallback(() => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page: 1 }
    }));
    loadFolderContents(state.currentFolder?.id);
  }, [state.currentFolder?.id, loadFolderContents]);

  // Handle sorting
  const handleSort = (field: SortField) => {
    setState(prev => ({
      ...prev,
      sorting: {
        field,
        direction: prev.sorting.field === field && prev.sorting.direction === SortDirection.ASC
          ? SortDirection.DESC
          : SortDirection.ASC
      },
      pagination: { ...prev.pagination, page: 1 }
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page }
    }));
  };

  // Handle item selection
  const toggleItemSelection = (itemId: string) => {
    setState(prev => {
      const newSelection = new Set(prev.selectedItems);
      if (newSelection.has(itemId)) {
        newSelection.delete(itemId);
      } else {
        newSelection.add(itemId);
      }
      return { 
        ...prev, 
        selectedItems: newSelection,
        showBulkActions: newSelection.size > 0
      };
    });
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    setState(prev => {
      const newSelection = new Set<string>();
      if (checked) {
        [...prev.folders, ...prev.files].forEach(item => newSelection.add(item.id));
      }
      return { 
        ...prev, 
        selectedItems: newSelection,
        showBulkActions: newSelection.size > 0
      };
    });
  };

  // Create new folder
  const handleCreateFolder = async (name: string, description?: string) => {
    try {
      const createData: FolderCreateData = {
        name,
        description,
        parentFolderId: state.currentFolder?.id
      };
      await folderService.create(createData);
      setState(prev => ({ ...prev, showNewFolderModal: false }));
      loadFolderContents(state.currentFolder?.id);
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to create folder'
      }));
    }
  };

  // Bulk delete
  const handleBulkDelete = async () => {
    try {
      const folderIds = Array.from(state.selectedItems).filter(id => 
        state.folders.some(f => f.id === id)
      );
      const fileIds = Array.from(state.selectedItems).filter(id => 
        state.files.some(f => f.id === id)
      );

      await Promise.all([
        folderIds.length > 0 ? folderService.bulkDelete(folderIds) : Promise.resolve(),
        fileIds.length > 0 ? fileService.bulkDelete(fileIds) : Promise.resolve()
      ]);

      setState(prev => ({ 
        ...prev, 
        selectedItems: new Set(),
        showBulkActions: false
      }));
      loadFolderContents(state.currentFolder?.id);
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to delete items'
      }));
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  };

  // Loading state
  if (!user?.access_token) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-body text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 animate-fade-in">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-heading-2 text-gray-900">Quản lý Folder</h1>
              <p className="text-body text-gray-600 mt-1">
                Quản lý và tổ chức folders của bạn
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={() => setState(prev => ({ ...prev, showNewFolderModal: true }))}
                className="btn-primary flex items-center gap-2"
              >
                <FolderPlusIcon className="w-4 h-4" />
                Tạo Folder Mới
              </button>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {state.error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{state.error}</p>
              </div>
              <div className="ml-auto pl-3">
                <button
                  onClick={() => setState(prev => ({ ...prev, error: null }))}
                  className="text-red-400 hover:text-red-600"
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Breadcrumb */}
        {state.breadcrumb.length > 0 && (
          <nav className="flex mb-6 animate-fade-in" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-1">
              <li>
                <button
                  onClick={() => navigateToBreadcrumb(0)}
                  className="text-blue-600 hover:text-blue-800 font-medium transition-colors"
                >
                  Root
                </button>
              </li>
              {state.breadcrumb.map((folder, index) => (
                <li key={folder.id} className="flex items-center">
                  <ChevronRightIcon className="w-4 h-4 text-gray-400 mx-1" />
                  <button
                    onClick={() => navigateToBreadcrumb(index)}
                    className={`font-medium transition-colors ${
                      index === state.breadcrumb.length - 1
                        ? 'text-gray-900'
                        : 'text-blue-600 hover:text-blue-800'
                    }`}
                  >
                    {folder.name}
                  </button>
                </li>
              ))}
            </ol>
          </nav>
        )}

        {/* Filters and Search */}
        <div className="card p-6 mb-6 animate-fade-in">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Tìm kiếm theo Tên Folder/ File"
                  value={state.searchTerm}
                  onChange={(e) => setState(prev => ({ ...prev, searchTerm: e.target.value }))}
                  className="input pl-10 w-full"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-3">
              <select
                value={state.filters.folderType}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, folderType: e.target.value as any }
                }))}
                className="input"
              >
                <option value="all">Tất cả loại</option>
                <option value="public">Public</option>
                <option value="private">Private</option>
                <option value="shared">Shared</option>
              </select>

              <input
                type="text"
                placeholder="Người upload"
                value={state.filters.uploaderEmail}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, uploaderEmail: e.target.value }
                }))}
                className="input min-w-[200px]"
              />

              <button 
                onClick={handleSearch}
                className="btn-primary"
              >
                Tìm kiếm
              </button>
            </div>
          </div>
        </div>

        {/* Toolbar */}
        <div className="flex justify-between items-center mb-6 animate-fade-in">
          <div className="flex items-center gap-3">
            {state.showBulkActions && (
              <div className="flex items-center gap-2 animate-slide-in">
                <span className="text-sm text-gray-600">
                  {state.selectedItems.size} đã chọn
                </span>
                <button 
                  onClick={handleBulkDelete}
                  className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                  title="Xóa các mục đã chọn"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
                <button className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors">
                  <ArchiveBoxIcon className="w-4 h-4" />
                </button>
                <button className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors">
                  <ShareIcon className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setState(prev => ({
                ...prev,
                viewMode: prev.viewMode === 'list' ? 'grid' : 'list'
              }))}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              title={state.viewMode === 'list' ? 'Chế độ lưới' : 'Chế độ danh sách'}
            >
              {state.viewMode === 'list' ? 
                <ViewColumnsIcon className="w-4 h-4" /> : 
                <Bars3Icon className="w-4 h-4" />
              }
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="card animate-fade-in">
          {state.viewMode === 'list' ? (
            <>
              {/* Table Header */}
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
                  <div className="col-span-1">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      checked={state.selectedItems.size > 0 && state.selectedItems.size === (state.folders.length + state.files.length)}
                    />
                  </div>
                  <div className="col-span-1">Loại</div>
                  <div className="col-span-3">
                    <button
                      onClick={() => handleSort(SortField.Name)}
                      className="flex items-center gap-1 hover:text-gray-900 transition-colors"
                    >
                      Tên
                      {state.sorting.field === SortField.Name && (
                        <span className="text-xs">
                          {state.sorting.direction === SortDirection.ASC ? '↑' : '↓'}
                        </span>
                      )}
                    </button>
                  </div>
                  <div className="col-span-2">Người sở hữu</div>
                  <div className="col-span-2">
                    <button
                      onClick={() => handleSort(SortField.CreatedAt)}
                      className="flex items-center gap-1 hover:text-gray-900 transition-colors"
                    >
                      Sửa đổi lần cuối
                      {state.sorting.field === SortField.CreatedAt && (
                        <span className="text-xs">
                          {state.sorting.direction === SortDirection.ASC ? '↑' : '↓'}
                        </span>
                      )}
                    </button>
                  </div>
                  <div className="col-span-1">Kích thước</div>
                  <div className="col-span-2">Thao tác</div>
                </div>
              </div>

              {/* Items */}
              <div className="divide-y divide-gray-200">
                {state.loading ? (
                  <div className="px-6 py-12 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-sm text-gray-600">Đang tải...</p>
                  </div>
                ) : (
                  <>
                    {/* Folders */}
                    {state.folders.map((folder, index) => (
                      <div 
                        key={folder.id} 
                        className="px-6 py-4 hover:bg-gray-50 animate-fade-in transition-colors"
                        style={{ animationDelay: `${index * 0.05}s` }}
                      >
                        <div className="grid grid-cols-12 gap-4 items-center">
                          <div className="col-span-1">
                            <input
                              type="checkbox"
                              checked={state.selectedItems.has(folder.id)}
                              onChange={() => toggleItemSelection(folder.id)}
                              className="rounded border-gray-300"
                            />
                          </div>
                          <div className="col-span-1">
                            <FolderIcon className="w-6 h-6 text-blue-500" />
                          </div>
                          <div className="col-span-3">
                            <button
                              onClick={() => navigateToFolder(folder.id)}
                              className="font-medium text-blue-600 hover:text-blue-800 transition-colors"
                            >
                              {folder.name}
                            </button>
                            {folder.description && (
                              <p className="text-sm text-gray-500 mt-1">{folder.description}</p>
                            )}
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {folder.ownerId}
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {formatDate(folder.updatedAt)}
                          </div>
                          <div className="col-span-1 text-sm text-gray-600">
                            --
                          </div>
                          <div className="col-span-2">
                            <div className="flex items-center gap-1">
                              <button
                                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                title="Download"
                              >
                                <ArrowDownTrayIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-yellow-600 transition-colors"
                                title="Edit"
                              >
                                <PencilIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                                title="Share"
                              >
                                <ShareIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                title="Delete"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Files */}
                    {state.files.map((file, index) => (
                      <div 
                        key={file.id} 
                        className="px-6 py-4 hover:bg-gray-50 animate-fade-in transition-colors"
                        style={{ animationDelay: `${(state.folders.length + index) * 0.05}s` }}
                      >
                        <div className="grid grid-cols-12 gap-4 items-center">
                          <div className="col-span-1">
                            <input
                              type="checkbox"
                              checked={state.selectedItems.has(file.id)}
                              onChange={() => toggleItemSelection(file.id)}
                              className="rounded border-gray-300"
                            />
                          </div>
                          <div className="col-span-1">
                            <DocumentIcon className="w-6 h-6 text-gray-500" />
                          </div>
                          <div className="col-span-3">
                            <p className="font-medium text-gray-900">{file.name}</p>
                            {file.description && (
                              <p className="text-sm text-gray-500 mt-1">{file.description}</p>
                            )}
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {file.ownerId}
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {formatDate(file.updatedAt)}
                          </div>
                          <div className="col-span-1 text-sm text-gray-600">
                            {formatFileSize(file.fileSize)}
                          </div>
                          <div className="col-span-2">
                            <div className="flex items-center gap-1">
                              <button
                                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                title="Download"
                              >
                                <ArrowDownTrayIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-yellow-600 transition-colors"
                                title="Edit"
                              >
                                <PencilIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                                title="Share"
                              >
                                <ShareIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                title="Delete"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Empty State */}
                    {!state.loading && state.folders.length === 0 && state.files.length === 0 && (
                      <div className="px-6 py-12 text-center animate-fade-in">
                        <FolderIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          Không có folders hoặc files
                        </h3>
                        <p className="text-gray-500 mb-4">
                          Tạo folder mới để bắt đầu tổ chức files của bạn.
                        </p>
                        <button
                          onClick={() => setState(prev => ({ ...prev, showNewFolderModal: true }))}
                          className="btn-primary"
                        >
                          Tạo Folder Mới
                        </button>
                      </div>
                    )}
                  </>
                )}
              </div>
            </>
          ) : (
            /* Grid View */
            <div className="p-6">
              {state.loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-600">Đang tải...</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {/* Folders in Grid */}
                  {state.folders.map((folder, index) => (
                    <div 
                      key={folder.id} 
                      className="card p-4 hover:shadow-md transition-all cursor-pointer animate-fade-in"
                      style={{ animationDelay: `${index * 0.05}s` }}
                      onClick={() => navigateToFolder(folder.id)}
                    >
                      <div className="text-center">
                        <FolderIcon className="w-12 h-12 text-blue-500 mx-auto mb-2" />
                        <p className="text-sm font-medium text-gray-900 truncate" title={folder.name}>
                          {folder.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {folder.fileCount} files
                        </p>
                      </div>
                      <div className="mt-2 flex justify-center">
                        <input
                          type="checkbox"
                          checked={state.selectedItems.has(folder.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            toggleItemSelection(folder.id);
                          }}
                          className="rounded border-gray-300"
                        />
                      </div>
                    </div>
                  ))}

                  {/* Files in Grid */}
                  {state.files.map((file, index) => (
                    <div 
                      key={file.id} 
                      className="card p-4 hover:shadow-md transition-all animate-fade-in"
                      style={{ animationDelay: `${(state.folders.length + index) * 0.05}s` }}
                    >
                      <div className="text-center">
                        <DocumentIcon className="w-12 h-12 text-gray-500 mx-auto mb-2" />
                        <p className="text-sm font-medium text-gray-900 truncate" title={file.name}>
                          {file.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {formatFileSize(file.fileSize)}
                        </p>
                      </div>
                      <div className="mt-2 flex justify-center">
                        <input
                          type="checkbox"
                          checked={state.selectedItems.has(file.id)}
                          onChange={() => toggleItemSelection(file.id)}
                          className="rounded border-gray-300"
                        />
                      </div>
                    </div>
                  ))}

                  {/* Empty State for Grid */}
                  {!state.loading && state.folders.length === 0 && state.files.length === 0 && (
                    <div className="col-span-full text-center py-12 animate-fade-in">
                      <FolderIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Không có folders hoặc files
                      </h3>
                      <p className="text-gray-500 mb-4">
                        Tạo folder mới để bắt đầu tổ chức files của bạn.
                      </p>
                      <button
                        onClick={() => setState(prev => ({ ...prev, showNewFolderModal: true }))}
                        className="btn-primary"
                      >
                        Tạo Folder Mới
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Pagination */}
        {state.pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 animate-fade-in">
            <div className="text-sm text-gray-700">
              Hiển thị {((state.pagination.page - 1) * state.pagination.pageSize) + 1} đến{' '}
              {Math.min(state.pagination.page * state.pagination.pageSize, state.pagination.totalItems)} của{' '}
              {state.pagination.totalItems} kết quả
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => handlePageChange(state.pagination.page - 1)}
                disabled={state.pagination.page === 1}
                className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeftIcon className="w-4 h-4" />
              </button>
              
              {/* Page numbers */}
              {Array.from({ length: Math.min(5, state.pagination.totalPages) }, (_, i) => {
                const pageNumber = Math.max(1, state.pagination.page - 2) + i;
                if (pageNumber <= state.pagination.totalPages) {
                  return (
                    <button
                      key={pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        pageNumber === state.pagination.page
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                      }`}
                    >
                      {pageNumber}
                    </button>
                  );
                }
                return null;
              })}

              <button
                onClick={() => handlePageChange(state.pagination.page + 1)}
                disabled={state.pagination.page === state.pagination.totalPages}
                className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRightIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* New Folder Modal */}
      {state.showNewFolderModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Tạo Folder Mới</h3>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                const name = formData.get('name') as string;
                const description = formData.get('description') as string;
                if (name.trim()) {
                  handleCreateFolder(name.trim(), description.trim() || undefined);
                }
              }}
            >
              <div className="mb-4">
                <label htmlFor="folderName" className="block text-sm font-medium text-gray-700 mb-2">
                  Tên Folder *
                </label>
                <input
                  type="text"
                  id="folderName"
                  name="name"
                  required
                  className="input w-full"
                  placeholder="Nhập tên folder"
                  autoFocus
                />
              </div>
              <div className="mb-6">
                <label htmlFor="folderDescription" className="block text-sm font-medium text-gray-700 mb-2">
                  Mô tả
                </label>
                <textarea
                  id="folderDescription"
                  name="description"
                  rows={3}
                  className="input w-full resize-none"
                  placeholder="Nhập mô tả (tùy chọn)"
                />
              </div>
              <div className="flex gap-3 justify-end">
                <button
                  type="button"
                  onClick={() => setState(prev => ({ ...prev, showNewFolderModal: false }))}
                  className="btn-secondary"
                >
                  Hủy
                </button>
                <button
                  type="submit"
                  className="btn-primary"
                >
                  Tạo Folder
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
} 