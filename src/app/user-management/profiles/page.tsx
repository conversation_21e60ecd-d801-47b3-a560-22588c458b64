"use client";

import React from "react";
import Link from "next/link";
import {
  UserCircleIcon,
  PencilIcon,
  EyeIcon,
  ArrowLeftIcon,
} from "@heroicons/react/24/outline";
import Image from "next/image";
import { useAuth } from "@/contexts/AuthContext";

export default function UserProfilesPage() {
  const { userProfile, isLoadingProfile } = useAuth();

  // Use real user profile data if available, otherwise show mock data
  const userProfiles = userProfile ? [
    {
      id: userProfile.id,
      name: userProfile.name || `${userProfile.firstName || ''} ${userProfile.lastName || ''}`.trim(),
      email: userProfile.email,
      avatar: userProfile.avatar,
      department: userProfile.department || "N/A",
      position: userProfile.position || "N/A",
      joinDate: userProfile.joinDate || "N/A",
      phone: userProfile.phone || "N/A",
      address: userProfile.address || "N/A",
      bio: userProfile.bio || "No bio available",
      skills: [], // This would come from API if available
    }
  ] : [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON><PERSON>n A",
      email: "<EMAIL>",
      avatar: null,
      department: "IT Department",
      position: "Senior Developer",
      joinDate: "2024-01-01",
      phone: "0123456789",
      address: "Hà Nội, Việt Nam",
      bio: "Experienced developer with 5+ years in web development.",
      skills: ["React", "Node.js", "TypeScript", "Python"],
    },
    {
      id: 2,
      name: "Trần Thị B",
      email: "<EMAIL>",
      avatar: null,
      department: "Marketing",
      position: "Marketing Manager",
      joinDate: "2024-01-02",
      phone: "0987654321",
      address: "TP.HCM, Việt Nam",
      bio: "Creative marketing professional with expertise in digital campaigns.",
      skills: ["Digital Marketing", "SEO", "Content Strategy", "Analytics"],
    },
  ];

  if (isLoadingProfile) {
    return (
      <div className="p-6">
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <Link
              href="/user-management"
              className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <UserCircleIcon className="h-8 w-8 text-gray-700 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">Hồ sơ người dùng</h1>
          </div>
          <p className="text-gray-600">
            Quản lý thông tin chi tiết và hồ sơ của người dùng trong hệ thống.
          </p>
        </div>

        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Đang tải thông tin người dùng...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Link
            href="/user-management"
            className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <UserCircleIcon className="h-8 w-8 text-gray-700 mr-3" />
          <h1 className="text-3xl font-bold text-gray-900">Hồ sơ người dùng</h1>
        </div>
        <p className="text-gray-600">
          Quản lý thông tin chi tiết và hồ sơ của người dùng trong hệ thống.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {userProfiles.map((profile) => (
          <div
            key={profile.id}
            className="bg-white rounded-lg border border-gray-200 shadow-sm"
          >
            {/* Profile Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div className="h-16 w-16 bg-gray-300 rounded-full flex items-center justify-center">
                    {profile.avatar ? (
                      <Image
                        src={profile.avatar}
                        alt={profile.name}
                        width={64}
                        height={64}
                        className="h-16 w-16 rounded-full"
                      />
                    ) : (
                      <UserCircleIcon className="h-10 w-10 text-gray-600" />
                    )}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {profile.name}
                    </h3>
                    <p className="text-sm text-gray-600">{profile.email}</p>
                    <p className="text-sm text-blue-600">{profile.position}</p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-md">
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-green-600 hover:bg-green-50 rounded-md">
                    <PencilIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Profile Details */}
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">
                    Phòng ban
                  </h4>
                  <p className="text-sm text-gray-900">{profile.department}</p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">
                    Ngày gia nhập
                  </h4>
                  <p className="text-sm text-gray-900">{profile.joinDate}</p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">
                    Số điện thoại
                  </h4>
                  <p className="text-sm text-gray-900">{profile.phone}</p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">
                    Địa chỉ
                  </h4>
                  <p className="text-sm text-gray-900">{profile.address}</p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">
                    Giới thiệu
                  </h4>
                  <p className="text-sm text-gray-900">{profile.bio}</p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">
                    Kỹ năng
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {profile.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Profile Actions */}
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
              <div className="flex justify-between items-center">
                <div className="text-xs text-gray-500">
                  Cập nhật lần cuối: 2024-01-15
                </div>
                <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                  Xem chi tiết →
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add New Profile Button */}
      <div className="mt-8 text-center">
        <Link
          href="/user-management/add"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <UserCircleIcon className="h-5 w-5 mr-2" />
          Tạo hồ sơ người dùng mới
        </Link>
      </div>

      {/* Statistics */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-blue-600">2</div>
          <div className="text-sm text-gray-600">Tổng số hồ sơ</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-green-600">2</div>
          <div className="text-sm text-gray-600">Hồ sơ hoàn thiện</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-orange-600">0</div>
          <div className="text-sm text-gray-600">Cần cập nhật</div>
        </div>
      </div>
    </div>
  );
}
