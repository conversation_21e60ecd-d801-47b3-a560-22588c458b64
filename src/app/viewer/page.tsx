"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import { useSearchParams } from "next/navigation";
import { FileService } from "@/feature/file-manager/file-service";

const PdfViewer = dynamic(
  () =>
    import("@/components/ui/PdfViewer").then((mod) => ({
      default: mod.PdfViewer,
    })),
  {
    ssr: false,
    loading: () => (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <div className="mt-4 text-gray-600">Loading PDF Viewer...</div>
        </div>
      </div>
    ),
  }
);
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon,
  ArrowPathIcon,
  ArrowUturnLeftIcon,
  DocumentDuplicateIcon,
} from "@heroicons/react/24/outline";

export default function ViewerPage() {
  const searchParams = useSearchParams();
  const fileId = searchParams.get("fileId");

  const [fileUrl, setFileUrl] = useState<string>("");
  const [fileName, setFileName] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [scale, setScale] = useState<number>(1.0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [inputValue, setInputValue] = useState<string>("");
  const [totalPages, setTotalPages] = useState<number>(0);

  useEffect(() => {
    const loadPdf = async () => {
      if (!fileId) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        // Get file details
        const fileDetails = await FileService.getFileDetails(fileId);
        setFileName(fileDetails.name);

        // Get download URL
        const downloadData = await FileService.getFileDownloadUrl(fileId);
        setFileUrl(downloadData.url);
      } catch (error) {
        console.error("Error loading PDF:", error);
        setError("Failed to load PDF file. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    loadPdf();
  }, [fileId]);

  // Zoom functions
  const zoomIn = () => setScale((prev) => Math.min(prev + 0.25, 3.0));
  const zoomOut = () => setScale((prev) => Math.max(prev - 0.25, 0.25));
  const resetZoom = () => setScale(1.0);

  // Navigation functions
  const canGoNext = currentPage < totalPages;
  const canGoPrevious = currentPage > 1;

  const jumpToNext = () => {
    if (canGoNext) setCurrentPage((prev) => prev + 1);
  };

  const jumpToPrevious = () => {
    if (canGoPrevious) setCurrentPage((prev) => prev - 1);
  };

  const jumpToPage = (page: number) => {
    const validPage = Math.max(1, Math.min(page, totalPages));
    setCurrentPage(validPage);
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);
  };

  const handleInputSubmit = () => {
    const page = parseInt(inputValue);
    if (!isNaN(page)) {
      jumpToPage(page);
    }
    setInputValue("");
  };

  const handleManualFileUrlChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFileUrl(e.target.value);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleScaleChange = (newScale: number) => {
    setScale(newScale);
  };

  const handleTotalPagesChange = (pages: number) => {
    setTotalPages(pages);
  };

  const handleBackToDashboard = () => {
    window.location.href = "/";
  };

  const handleDownloadPdf = async () => {
    if (fileId) {
      try {
        await FileService.downloadFile(fileId, fileName);
      } catch (error) {
        console.error("Error downloading file:", error);
        setError("Failed to download file. Please try again.");
      }
    } else if (fileUrl) {
      // Direct download from URL
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName || "download.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Toolbar */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Back button and file name */}
          <div className="flex items-center">
            <button
              onClick={handleBackToDashboard}
              className="p-2 mr-2 text-gray-500 hover:text-gray-700"
              title="Back to dashboard"
            >
              <ArrowUturnLeftIcon className="h-5 w-5" />
            </button>

            {fileName ? (
              <h2 className="text-lg font-medium text-gray-900 truncate max-w-xs">
                {fileName}
              </h2>
            ) : (
              <div className="flex-1 max-w-md">
                <input
                  type="url"
                  value={fileUrl}
                  onChange={handleManualFileUrlChange}
                  placeholder="Enter PDF URL..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            )}
          </div>

          {/* Navigation Controls */}
          <div className="flex items-center gap-2">
            <button
              onClick={jumpToPrevious}
              disabled={!canGoPrevious}
              className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
              title="Previous page"
            >
              <ChevronLeftIcon className="h-5 w-5" />
            </button>

            <div className="flex items-center gap-2">
              <input
                type="text"
                value={inputValue || currentPage}
                onChange={(e) => handleInputChange(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleInputSubmit()}
                onBlur={handleInputSubmit}
                className="w-12 px-2 py-1 text-center border border-gray-300 rounded text-sm"
              />
              <span className="text-sm text-gray-600">of {totalPages}</span>
            </div>

            <button
              onClick={jumpToNext}
              disabled={!canGoNext}
              className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
              title="Next page"
            >
              <ChevronRightIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Actions and Zoom Controls */}
          <div className="flex items-center gap-2">
            {/* Download button */}
            <button
              onClick={handleDownloadPdf}
              className="p-2 text-gray-400 hover:text-gray-600"
              title="Download PDF"
            >
              <DocumentDuplicateIcon className="h-5 w-5" />
            </button>

            {/* Zoom controls */}
            <button
              onClick={zoomOut}
              className="p-2 text-gray-400 hover:text-gray-600"
              title="Zoom out"
            >
              <MagnifyingGlassMinusIcon className="h-5 w-5" />
            </button>

            <span className="text-sm text-gray-600 min-w-[4rem] text-center">
              {Math.round(scale * 100)}%
            </span>

            <button
              onClick={zoomIn}
              className="p-2 text-gray-400 hover:text-gray-600"
              title="Zoom in"
            >
              <MagnifyingGlassPlusIcon className="h-5 w-5" />
            </button>

            <button
              onClick={resetZoom}
              className="p-2 text-gray-400 hover:text-gray-600"
              title="Reset zoom"
            >
              <ArrowPathIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 m-4 rounded relative">
          {error}
          <button
            className="absolute top-0 right-0 p-2"
            onClick={() => setError(null)}
          >
            <span className="sr-only">Close</span>
            <svg
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      )}

      {/* PDF Viewer */}
      <div className="flex-1 overflow-hidden">
        {isLoading ? (
          <div className="h-full flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <div className="mt-4 text-gray-600">Loading PDF...</div>
            </div>
          </div>
        ) : fileUrl ? (
          <PdfViewer
            fileUrl={fileUrl}
            onPageChange={handlePageChange}
            onScaleChange={handleScaleChange}
            onTotalPagesChange={handleTotalPagesChange}
            initialPage={currentPage}
            initialScale={scale}
          />
        ) : (
          <div className="h-full flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <div className="text-gray-400 text-lg mb-2">No PDF loaded</div>
              <div className="text-gray-500 text-sm">
                {fileId
                  ? "Failed to load PDF file"
                  : "Enter a PDF URL above to start viewing"}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
