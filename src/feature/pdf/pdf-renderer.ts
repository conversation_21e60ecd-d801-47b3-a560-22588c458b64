import * as pdfjsLib from "pdfjs-dist";
import { PDFRenderOptions, PDFPage } from "@/types";

// Configure PDF.js worker
if (typeof window !== "undefined") {
  pdfjsLib.GlobalWorkerOptions.workerSrc = "/pdf.worker.min.js";
}

export class PDFRenderer {
  private document: pdfjsLib.PDFDocumentProxy | null = null;
  private pages: Map<number, pdfjsLib.PDFPageProxy> = new Map();

  /**
   * Load PDF document from URL
   */
  async loadDocument(url: string): Promise<pdfjsLib.PDFDocumentProxy> {
    try {
      this.document = await pdfjsLib.getDocument(url).promise;
      this.pages.clear();
      return this.document;
    } catch (error) {
      console.error("Error loading PDF document:", error);
      throw new Error("Failed to load PDF document");
    }
  }

  /**
   * Get page from document
   */
  async getPage(pageNumber: number): Promise<pdfjsLib.PDFPageProxy> {
    if (!this.document) {
      throw new Error("No document loaded");
    }

    if (this.pages.has(pageNumber)) {
      return this.pages.get(pageNumber)!;
    }

    try {
      const page = await this.document.getPage(pageNumber);
      this.pages.set(pageNumber, page);
      return page;
    } catch (error) {
      console.error(`Error loading page ${pageNumber}:`, error);
      throw new Error(`Failed to load page ${pageNumber}`);
    }
  }

  /**
   * Render page to canvas
   */
  async renderPage(
    pageNumber: number,
    canvas: HTMLCanvasElement,
    options: PDFRenderOptions = { scale: 1, rotation: 0 }
  ): Promise<PDFPage> {
    const page = await this.getPage(pageNumber);
    const context = canvas.getContext("2d");

    if (!context) {
      throw new Error("Canvas context not available");
    }

    // Get viewport with scale and rotation
    const viewport = page.getViewport({
      scale: options.scale,
      rotation: options.rotation,
    });

    // Set canvas dimensions
    canvas.width = viewport.width;
    canvas.height = viewport.height;

    // Render page
    const renderContext = {
      canvasContext: context,
      viewport: viewport,
    };

    try {
      await page.render(renderContext).promise;

      return {
        pageNumber,
        width: viewport.width,
        height: viewport.height,
        scale: options.scale,
      };
    } catch (error) {
      console.error(`Error rendering page ${pageNumber}:`, error);
      throw new Error(`Failed to render page ${pageNumber}`);
    }
  }

  /**
   * Get page dimensions
   */
  async getPageDimensions(
    pageNumber: number,
    scale: number = 1
  ): Promise<{ width: number; height: number }> {
    const page = await this.getPage(pageNumber);
    const viewport = page.getViewport({ scale });

    return {
      width: viewport.width,
      height: viewport.height,
    };
  }

  /**
   * Get document info
   */
  getDocumentInfo(): { numPages: number } | null {
    if (!this.document) return null;

    return {
      numPages: this.document.numPages,
    };
  }

  /**
   * Extract text from page
   */
  async extractTextFromPage(pageNumber: number): Promise<string> {
    const page = await this.getPage(pageNumber);

    try {
      const textContent = await page.getTextContent();
      return textContent.items.map((item: any) => item.str).join(" ");
    } catch (error) {
      console.error(`Error extracting text from page ${pageNumber}:`, error);
      throw new Error(`Failed to extract text from page ${pageNumber}`);
    }
  }

  /**
   * Extract text from all pages
   */
  async extractTextFromDocument(): Promise<string[]> {
    if (!this.document) {
      throw new Error("No document loaded");
    }

    const texts: string[] = [];
    const numPages = this.document.numPages;

    for (let i = 1; i <= numPages; i++) {
      try {
        const text = await this.extractTextFromPage(i);
        texts.push(text);
      } catch (error) {
        console.error(`Error extracting text from page ${i}:`, error);
        texts.push(""); // Add empty string for failed pages
      }
    }

    return texts;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.pages.clear();
    if (this.document) {
      this.document.destroy();
      this.document = null;
    }
  }
}
