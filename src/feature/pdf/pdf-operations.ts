import { apiClient } from '@/lib/api-client';
import { API_ENDPOINTS } from '@/lib/constants';
import { SplitJob, OCRJob, ApiResponse } from '@/types';

export interface SplitPDFRequest {
  fileId: string;
  ranges: Array<{
    start: number;
    end: number;
    name: string;
  }>;
}

export interface OCRRequest {
  fileId: string;
  language?: string;
  options?: {
    detectOrientation?: boolean;
    preserveLayout?: boolean;
  };
}

export interface MergePDFRequest {
  fileIds: string[];
  outputName: string;
}

export class PDFOperations {
  /**
   * Split PDF into multiple files
   */
  static async splitPDF(request: SplitPDFRequest): Promise<SplitJob> {
    try {
      const response = await apiClient.post<ApiResponse<SplitJob>>(
        API_ENDPOINTS.PDF_SPLIT,
        request
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to split PDF');
      }

      return response.data;
    } catch (error) {
      console.error('Error splitting PDF:', error);
      throw error;
    }
  }

  /**
   * Perform OCR on PDF
   */
  static async performOCR(request: OCRRequest): Promise<OCRJob> {
    try {
      const response = await apiClient.post<ApiResponse<OCRJob>>(
        API_ENDPOINTS.PDF_OCR,
        request
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to perform OCR');
      }

      return response.data;
    } catch (error) {
      console.error('Error performing OCR:', error);
      throw error;
    }
  }

  /**
   * Merge multiple PDFs
   */
  static async mergePDFs(request: MergePDFRequest): Promise<SplitJob> {
    try {
      const response = await apiClient.post<ApiResponse<SplitJob>>(
        API_ENDPOINTS.PDF_MERGE,
        request
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to merge PDFs');
      }

      return response.data;
    } catch (error) {
      console.error('Error merging PDFs:', error);
      throw error;
    }
  }

  /**
   * Get supported OCR languages
   */
  static async getSupportedLanguages(): Promise<Array<{ code: string; name: string }>> {
    try {
      const response = await apiClient.get<ApiResponse<Array<{ code: string; name: string }>>>(
        `${API_ENDPOINTS.PDF_OCR}/languages`
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to get supported languages');
      }

      return response.data;
    } catch (error) {
      console.error('Error getting supported languages:', error);
      // Return default languages if API fails
      return [
        { code: 'eng', name: 'English' },
        { code: 'spa', name: 'Spanish' },
        { code: 'fra', name: 'French' },
        { code: 'deu', name: 'German' },
        { code: 'ita', name: 'Italian' },
        { code: 'por', name: 'Portuguese' },
        { code: 'rus', name: 'Russian' },
        { code: 'jpn', name: 'Japanese' },
        { code: 'chi_sim', name: 'Chinese (Simplified)' },
        { code: 'chi_tra', name: 'Chinese (Traditional)' },
      ];
    }
  }

  /**
   * Validate page ranges for splitting
   */
  static validateSplitRanges(
    ranges: Array<{ start: number; end: number; name: string }>,
    totalPages: number
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (ranges.length === 0) {
      errors.push('At least one range is required');
      return { isValid: false, errors };
    }

    for (let i = 0; i < ranges.length; i++) {
      const range = ranges[i];

      // Check if range is valid
      if (range.start < 1 || range.start > totalPages) {
        errors.push(`Range ${i + 1}: Start page must be between 1 and ${totalPages}`);
      }

      if (range.end < 1 || range.end > totalPages) {
        errors.push(`Range ${i + 1}: End page must be between 1 and ${totalPages}`);
      }

      if (range.start > range.end) {
        errors.push(`Range ${i + 1}: Start page cannot be greater than end page`);
      }

      if (!range.name.trim()) {
        errors.push(`Range ${i + 1}: Name is required`);
      }

      // Check for overlapping ranges
      for (let j = i + 1; j < ranges.length; j++) {
        const otherRange = ranges[j];
        if (
          (range.start <= otherRange.end && range.end >= otherRange.start) ||
          (otherRange.start <= range.end && otherRange.end >= range.start)
        ) {
          errors.push(`Range ${i + 1} overlaps with range ${j + 1}`);
        }
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Generate suggested split ranges
   */
  static generateSuggestedRanges(totalPages: number, rangesCount: number = 2): Array<{
    start: number;
    end: number;
    name: string;
  }> {
    const ranges: Array<{ start: number; end: number; name: string }> = [];
    const pagesPerRange = Math.ceil(totalPages / rangesCount);

    for (let i = 0; i < rangesCount; i++) {
      const start = i * pagesPerRange + 1;
      const end = Math.min((i + 1) * pagesPerRange, totalPages);
      
      if (start <= totalPages) {
        ranges.push({
          start,
          end,
          name: `Part ${i + 1} (Pages ${start}-${end})`,
        });
      }
    }

    return ranges;
  }
}
