import { apiClient } from "@/lib/api-client";
import { API_ENDPOINTS } from "@/lib/constants";
import { FileItem, Folder, ApiResponse, PaginatedResponse } from "@/types";
import axios from "axios";

export interface FileListQuery {
  folderId?: string;
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: "name" | "size" | "createdAt" | "updatedAt";
  sortOrder?: "asc" | "desc";
}

export interface CreateFolderRequest {
  name: string;
  parentId?: string;
}

export class FileService {
  /**
   * Get list of files and folders
   */
  static async getFiles(query: FileListQuery = {}): Promise<{
    files: FileItem[];
    folders: Folder[];
    pagination?: any;
  }> {
    try {
      const params = new URLSearchParams();

      if (query.folderId) params.append("folderId", query.folderId);
      if (query.page) params.append("page", query.page.toString());
      if (query.limit) params.append("limit", query.limit.toString());
      if (query.search) params.append("search", query.search);
      if (query.sortBy) params.append("sortBy", query.sortBy);
      if (query.sortOrder) params.append("sortOrder", query.sortOrder);

      const response = await apiClient.get<
        PaginatedResponse<FileItem | Folder>
      >(`${API_ENDPOINTS.FILES}?${params.toString()}`);

      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to fetch files");
      }

      // Separate files and folders
      const files = response.data.filter(
        (item) => item.type === "file"
      ) as FileItem[];
      const folders = response.data.filter(
        (item) => item.type === "folder"
      ) as Folder[];

      return {
        files,
        folders,
        pagination: response.pagination,
      };
    } catch (error) {
      console.error("Error fetching files:", error);
      throw error;
    }
  }

  static async uploadFile(
    file: File,
    folderId?: string,
    onProgress?: (progress: number) => void
  ): Promise<FileItem> {
    try {
      const additionalData: Record<string, any> = {};
      if (folderId) {
        additionalData.folderId = folderId;
      }

      const response = await apiClient.uploadFile<ApiResponse<FileItem>>(
        API_ENDPOINTS.FILE_UPLOAD,
        file,
        onProgress,
        additionalData
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to upload file");
      }

      return response.data;
    } catch (error) {
      console.error("Error uploading file:", error);
      throw error;
    }
  }

  /**
   * Delete file
   */
  static async deleteFile(fileId: string): Promise<void> {
    try {
      const response = await apiClient.delete<ApiResponse>(
        API_ENDPOINTS.FILE_DELETE(fileId)
      );

      if (!response.success) {
        throw new Error(response.error || "Failed to delete file");
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      throw error;
    }
  }

  /**
   * Download file
   */
  static async downloadFile(fileId: string, filename?: string): Promise<void> {
    try {
      await apiClient.downloadFile(
        API_ENDPOINTS.FILE_DOWNLOAD(fileId),
        filename
      );
    } catch (error) {
      console.error("Error downloading file:", error);
      throw error;
    }
  }

  /**
   * Create folder
   */
  static async createFolder(request: CreateFolderRequest): Promise<Folder> {
    try {
      const response = await apiClient.post<ApiResponse<Folder>>(
        API_ENDPOINTS.FOLDER_CREATE,
        request
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to create folder");
      }

      return response.data;
    } catch (error) {
      console.error("Error creating folder:", error);
      throw error;
    }
  }

  /**
   * Delete folder
   */
  static async deleteFolder(folderId: string): Promise<void> {
    try {
      const response = await apiClient.delete<ApiResponse>(
        API_ENDPOINTS.FOLDER_DELETE(folderId)
      );

      if (!response.success) {
        throw new Error(response.error || "Failed to delete folder");
      }
    } catch (error) {
      console.error("Error deleting folder:", error);
      throw error;
    }
  }

  /**
   * Get folder tree
   */
  static async getFolderTree(): Promise<Folder[]> {
    try {
      const response = await apiClient.get<ApiResponse<Folder[]>>(
        `${API_ENDPOINTS.FOLDERS}/tree`
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to fetch folder tree");
      }

      return response.data;
    } catch (error) {
      console.error("Error fetching folder tree:", error);
      throw error;
    }
  }

  /**
   * Move file to folder
   */
  static async moveFile(
    fileId: string,
    targetFolderId?: string
  ): Promise<FileItem> {
    try {
      const response = await apiClient.put<ApiResponse<FileItem>>(
        `${API_ENDPOINTS.FILES}/${fileId}/move`,
        { folderId: targetFolderId }
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to move file");
      }

      return response.data;
    } catch (error) {
      console.error("Error moving file:", error);
      throw error;
    }
  }

  /**
   * Rename file
   */
  static async renameFile(fileId: string, newName: string): Promise<FileItem> {
    try {
      const response = await apiClient.put<ApiResponse<FileItem>>(
        `${API_ENDPOINTS.FILES}/${fileId}`,
        { name: newName }
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to rename file");
      }

      return response.data;
    } catch (error) {
      console.error("Error renaming file:", error);
      throw error;
    }
  }

  /**
   * Get file details
   */
  static async getFileDetails(fileId: string): Promise<FileItem> {
    try {
      const response = await apiClient.get<ApiResponse<FileItem>>(
        `${API_ENDPOINTS.FILES}/${fileId}`
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to fetch file details");
      }

      return response.data;
    } catch (error) {
      console.error("Error fetching file details:", error);
      throw error;
    }
  }

  /**
   * Get file download URL
   */
  static async getFileDownloadUrl(
    fileId: string
  ): Promise<{ url: string; expires: string }> {
    try {
      const response = await apiClient.get<
        ApiResponse<{ url: string; expires: string }>
      >(`${API_ENDPOINTS.FILE_DOWNLOAD(fileId)}?presigned=true`);

      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to get file download URL");
      }

      return response.data;
    } catch (error) {
      console.error("Error getting file download URL:", error);
      throw error;
    }
  }
}
