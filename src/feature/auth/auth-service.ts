import { OIDC_CONFIG_CONSTANTS } from "@/lib/constants";
import {
  parseTokenFromUrl,
  cleanUrlAfterTokenExtraction,
  setTokenInCookie,
  removeTokenFromCookie,
} from "@/lib/token-parser.ts";

export class AuthService {
  /**
   * Redirect to IdentityServer for authentication
   */
  static redirectToLogin(returnUrl?: string): void {
    const params = new URLSearchParams({
      client_id: process.env.NEXT_PUBLIC_CLIENT_ID || "pdf-ocr-dashboard",
      redirect_uri: `${window.location.origin}/auth/callback`,
      response_type: "token",
      scope: "openid profile email api",
      state: returnUrl || window.location.pathname,
    });

    const loginUrl = `${OIDC_CONFIG_CONSTANTS}/connect/authorize?${params.toString()}`;
    window.location.href = loginUrl;
  }

  /**
   * Handle callback from IdentityServer
   */
  static handleCallback(): {
    success: boolean;
    returnUrl?: string;
    error?: string;
  } {
    const { accessToken, error } = parseTokenFromUrl();

    if (error) {
      cleanUrlAfterTokenExtraction();
      return { success: false, error };
    }

    if (!accessToken) {
      cleanUrlAfterTokenExtraction();
      return { success: false, error: "No access token received" };
    }

    // Store token in cookie
    setTokenInCookie(accessToken);

    // Get return URL from state parameter
    const urlParams = new URLSearchParams(window.location.search);
    const hash = window.location.hash.substring(1);
    const hashParams = new URLSearchParams(hash);
    const returnUrl = urlParams.get("state") || hashParams.get("state") || "/";

    // Clean URL
    cleanUrlAfterTokenExtraction();

    return { success: true, returnUrl };
  }

  /**
   * Logout user
   */
  static logout(): void {
    removeTokenFromCookie();

    // Redirect to IdentityServer logout
    const params = new URLSearchParams({
      post_logout_redirect_uri: window.location.origin,
    });

    const logoutUrl = `${OIDC_CONFIG_CONSTANTS}/connect/endsession?${params.toString()}`;
    window.location.href = logoutUrl;
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    // This will be handled by the auth store
    return false;
  }
}
