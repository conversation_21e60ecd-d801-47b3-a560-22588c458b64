import { WebStorageStateStore, UserManagerSettings } from "oidc-client-ts";
import { AuthProviderProps } from "react-oidc-context";

// Environment variables with defaults
const getEnvVar = (name: string, defaultValue: string = ''): string => {
  return process.env[name] || defaultValue;
};

// Environment configuration
const env = {
  oidcAuthority: getEnvVar('NEXT_PUBLIC_OIDC_AUTHORITY', 'https://sso.veasy.vn'),
  oidcClientId: getEnvVar('NEXT_PUBLIC_OIDC_CLIENT_ID', 'veasy_web_client'),
  oidcRedirectUri: getEnvVar('NEXT_PUBLIC_OIDC_REDIRECT_URI', 'http://localhost:3000/auth/callback'),
  oidcPostLogoutRedirectUri: getEnvVar('NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI', 'http://localhost:3000/'),
  oidcSilentRedirectUri: getEnvVar('NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI', 'http://localhost:3000/auth/silent-callback'),
  oidcScope: getEnvVar('NEXT_PUBLIC_OIDC_SCOPE', 'openid profile email roles file_service'),
  oidcClientSecret: getEnvVar('NEXT_PUBLIC_OIDC_CLIENT_SECRET', 'secret'),
  apiBaseUrl: getEnvVar('NEXT_PUBLIC_API_BASE_URL', 'https://localhost:7040'),
};


// Create OIDC configuration following documentation guidelines
const createOidcConfig = (): AuthProviderProps => {
  // Validate required environment variables
  if (!env.oidcAuthority || !env.oidcClientId) {
    console.error('🚨 Missing required OIDC environment variables');
    console.error('Required: NEXT_PUBLIC_OIDC_AUTHORITY, NEXT_PUBLIC_OIDC_CLIENT_ID');
    
    if (!env.oidcAuthority) {
      console.error('❌ NEXT_PUBLIC_OIDC_AUTHORITY is not set or empty');
      console.error('   Please set it to your Identity Provider URL (e.g., https://sso.veasy.vn)');
      console.error('   Do NOT use http://localhost:3000 as this would create a self-referencing error');
    }
    
    if (!env.oidcClientId) {
      console.error('❌ NEXT_PUBLIC_OIDC_CLIENT_ID is not set or empty');
      console.error('   Please set it to your registered client ID from your Identity Provider');
    }
    
    console.error('📝 Common issues to check:');
    console.error('   1. Environment file should be named ".env.local" (not ".envlocal")');
    console.error('   2. Restart development server after creating/editing .env.local');
    console.error('   3. Make sure .env.local is in the project root directory');
    console.error('   4. Check that variables start with NEXT_PUBLIC_');
    
    // Return a dummy configuration to prevent crashes
    return {
      authority: 'https://example.com', 
      client_id: 'not-configured',
      redirect_uri: env.oidcRedirectUri,
      post_logout_redirect_uri: env.oidcPostLogoutRedirectUri,
      response_type: "code",
      scope: "openid",
    } as AuthProviderProps;
  }

  // Create UserManager settings
  const userManagerSettings: UserManagerSettings = {
    // Core OIDC settings
    authority: env.oidcAuthority,
    client_id: env.oidcClientId,
    redirect_uri: env.oidcRedirectUri,
    post_logout_redirect_uri: env.oidcPostLogoutRedirectUri,
    
    // Flow configuration - Authorization Code with PKCE (recommended for SPAs)
    response_type: "code",
    response_mode: "fragment", // Better for SPAs according to documentation
    scope: env.oidcScope,
    
    // Token management
    automaticSilentRenew: true,
    loadUserInfo: true,
    includeIdTokenInSilentRenew: true,
    revokeTokensOnSignout: true,
    
    // Session management
    monitorSession: true,
    
    // Silent renewal configuration
    silent_redirect_uri: env.oidcSilentRedirectUri,
    
    // Security settings
    validateSubOnSilentRenew: true,
    
    // Metadata URL
    metadataUrl: `${env.oidcAuthority}/.well-known/openid-configuration`,
    
    // Extra query parameters (can be used for custom parameters)
    extraQueryParams: {},
    
    // Client secret (only if using confidential client)
    ...(env.oidcClientSecret && { client_secret: env.oidcClientSecret }),
  };

  // Only add storage when running on client-side
  if (typeof window !== "undefined") {
    // Use sessionStorage for state (more secure) and localStorage for user data
    userManagerSettings.stateStore = new WebStorageStateStore({
      store: window.sessionStorage,
    });
    userManagerSettings.userStore = new WebStorageStateStore({
      store: window.localStorage,
    });
  }

  // Log configuration in development (without sensitive data)
  if (process.env.NODE_ENV === "development") {
    console.log("🔧 OIDC Configuration Loaded:", {
      authority: userManagerSettings.authority,
      client_id: userManagerSettings.client_id,
      redirect_uri: userManagerSettings.redirect_uri,
      scope: userManagerSettings.scope,
      response_type: userManagerSettings.response_type,
      response_mode: userManagerSettings.response_mode,
      automaticSilentRenew: userManagerSettings.automaticSilentRenew,
      client_secret: userManagerSettings.client_secret ? '[CONFIGURED]' : '[NOT SET]',
    });
  }

  // Return as AuthProviderProps (UserManagerSettings is compatible)
  return userManagerSettings as AuthProviderProps;
};

// Export the configuration
export const oidcConfig = createOidcConfig();

// Export environment variables for use in other parts of the app
export { env };
