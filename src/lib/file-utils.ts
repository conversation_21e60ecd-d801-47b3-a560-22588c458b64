/**
 * File utility functions for VeasyFileManager
 * Provides helper functions for file operations, validation, and formatting
 */

import { FileDto, ValidationError, ErrorCode } from '../api/types/interfaces';

/**
 * Format file size in human-readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  return filename.substring(filename.lastIndexOf('.')).toLowerCase();
}

/**
 * Get MIME type category
 */
export function getMimeTypeCategory(mimeType: string): string {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';
  if (mimeType.startsWith('text/')) return 'text';
  if (mimeType.includes('pdf')) return 'pdf';
  if (mimeType.includes('word') || mimeType.includes('document')) return 'document';
  if (mimeType.includes('sheet') || mimeType.includes('excel')) return 'spreadsheet';
  if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return 'presentation';
  if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('archive')) return 'archive';
  return 'other';
}

/**
 * Enhanced file validation with security checks
 */
export interface FileValidationOptions {
  maxSize?: number;
  allowedTypes?: string[];
  allowedExtensions?: string[];
  blockDangerousTypes?: boolean;
  requireNonEmpty?: boolean;
}

export interface FileValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: string[];
}

export function validateFile(file: File, options: FileValidationOptions = {}): FileValidationResult {
  const {
    maxSize = 100 * 1024 * 1024, // 100MB default
    allowedTypes = [],
    allowedExtensions = [],
    blockDangerousTypes = true,
    requireNonEmpty = true
  } = options;

  const errors: ValidationError[] = [];
  const warnings: string[] = [];

  // Check if file exists
  if (!file) {
    errors.push({
      field: 'file',
      message: 'File is required',
      code: 'REQUIRED'
    });
    return { isValid: false, errors, warnings };
  }

  // Check file size
  if (requireNonEmpty && file.size === 0) {
    errors.push({
      field: 'file',
      message: 'File cannot be empty',
      code: 'EMPTY_FILE'
    });
  }

  if (file.size > maxSize) {
    errors.push({
      field: 'file',
      message: `File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(maxSize)})`,
      code: ErrorCode.FILE_TOO_LARGE
    });
  }

  // Get file extension and MIME type
  const extension = getFileExtension(file.name);
  const mimeType = file.type;

  // Check dangerous file types
  if (blockDangerousTypes) {
    const dangerousExtensions = [
      '.exe', '.bat', '.cmd', '.com', '.scr', '.vbs', '.js', '.jar',
      '.app', '.deb', '.pkg', '.dmg', '.msi', '.ps1', '.sh', '.bin'
    ];

    if (dangerousExtensions.includes(extension)) {
      errors.push({
        field: 'file',
        message: `File type ${extension} is not allowed for security reasons`,
        code: ErrorCode.INVALID_FILE_TYPE
      });
    }
  }

  // Check allowed file types (MIME types)
  if (allowedTypes.length > 0) {
    const isAllowedType = allowedTypes.some(type => {
      if (type.includes('*')) {
        return mimeType.match(new RegExp(type.replace('*', '.*')));
      }
      return mimeType === type;
    });

    if (!isAllowedType) {
      errors.push({
        field: 'file',
        message: `File type ${mimeType} is not allowed`,
        code: ErrorCode.INVALID_FILE_TYPE
      });
    }
  }

  // Check allowed extensions
  if (allowedExtensions.length > 0) {
    const isAllowedExtension = allowedExtensions.some(ext =>
      extension === (ext.startsWith('.') ? ext.toLowerCase() : `.${ext.toLowerCase()}`)
    );

    if (!isAllowedExtension) {
      errors.push({
        field: 'file',
        message: `File extension ${extension} is not allowed`,
        code: ErrorCode.INVALID_FILE_TYPE
      });
    }
  }

  // MIME type vs extension validation
  const mimeValidation = validateMimeTypeMatchesExtension(mimeType, extension);
  if (!mimeValidation.isValid) {
    warnings.push(mimeValidation.message);
  }

  // Size warnings
  if (file.size > 50 * 1024 * 1024) { // 50MB
    warnings.push('Large file detected. Consider using chunked upload for better performance.');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validate that MIME type matches file extension
 */
export function validateMimeTypeMatchesExtension(mimeType: string, extension: string): { isValid: boolean; message: string } {
  const commonMimeTypes: Record<string, string[]> = {
    '.pdf': ['application/pdf'],
    '.doc': ['application/msword'],
    '.docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    '.xls': ['application/vnd.ms-excel'],
    '.xlsx': ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    '.ppt': ['application/vnd.ms-powerpoint'],
    '.pptx': ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
    '.jpg': ['image/jpeg'],
    '.jpeg': ['image/jpeg'],
    '.png': ['image/png'],
    '.gif': ['image/gif'],
    '.bmp': ['image/bmp'],
    '.webp': ['image/webp'],
    '.txt': ['text/plain'],
    '.csv': ['text/csv', 'application/csv'],
    '.json': ['application/json'],
    '.xml': ['application/xml', 'text/xml'],
    '.zip': ['application/zip'],
    '.rar': ['application/x-rar-compressed'],
    '.7z': ['application/x-7z-compressed'],
    '.mp4': ['video/mp4'],
    '.avi': ['video/x-msvideo'],
    '.mov': ['video/quicktime'],
    '.mp3': ['audio/mpeg'],
    '.wav': ['audio/wav'],
    '.ogg': ['audio/ogg']
  };

  const expectedMimeTypes = commonMimeTypes[extension];
  if (expectedMimeTypes && !expectedMimeTypes.includes(mimeType)) {
    return {
      isValid: false,
      message: `File extension ${extension} does not match MIME type ${mimeType}. This may indicate a renamed file.`
    };
  }

  return { isValid: true, message: '' };
}

/**
 * Generate a safe filename by removing dangerous characters
 */
export function sanitizeFilename(filename: string): string {
  // Remove dangerous characters
  const dangerous = /[<>:"/\\|?*\x00-\x1f]/g;
  let sanitized = filename.replace(dangerous, '_');

  // Remove leading/trailing dots and spaces
  sanitized = sanitized.replace(/^[.\s]+|[.\s]+$/g, '');

  // Ensure it's not empty
  if (sanitized.length === 0) {
    sanitized = 'file';
  }

  // Limit length
  if (sanitized.length > 255) {
    const ext = getFileExtension(sanitized);
    const nameWithoutExt = sanitized.substring(0, sanitized.lastIndexOf('.'));
    sanitized = nameWithoutExt.substring(0, 255 - ext.length) + ext;
  }

  return sanitized;
}

/**
 * Check if file should use chunked upload
 */
export function shouldUseChunkedUpload(file: File, threshold: number = 50 * 1024 * 1024): boolean {
  return file.size > threshold;
}

/**
 * Calculate optimal chunk size based on file size
 */
export function calculateOptimalChunkSize(fileSize: number): number {
  if (fileSize < 100 * 1024 * 1024) return 5 * 1024 * 1024;    // 5MB for files < 100MB
  if (fileSize < 1024 * 1024 * 1024) return 10 * 1024 * 1024;  // 10MB for files < 1GB
  if (fileSize < 5 * 1024 * 1024 * 1024) return 25 * 1024 * 1024; // 25MB for files < 5GB
  return 50 * 1024 * 1024; // 50MB for larger files
}

/**
 * Convert file to base64 data URL
 */
export function fileToDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = () => reject(reader.error);
    reader.readAsDataURL(file);
  });
}

/**
 * Create a preview URL for supported file types
 */
export function createFilePreviewUrl(file: File): string | null {
  const mimeCategory = getMimeTypeCategory(file.type);

  if (['image', 'video', 'audio'].includes(mimeCategory)) {
    return URL.createObjectURL(file);
  }

  return null;
}

/**
 * Release a preview URL created with createFilePreviewUrl
 */
export function releaseFilePreviewUrl(url: string): void {
  URL.revokeObjectURL(url);
}

/**
 * Get file icon class based on file type
 */
export function getFileIconClass(file: FileDto | File): string {
  const mimeType = 'mimeType' in file ? file.mimeType : file.type;
  const fileName = 'name' in file ? file.name : file;
  const extension = getFileExtension(fileName);
  const category = getMimeTypeCategory(mimeType);

  switch (category) {
    case 'image': return 'fa-file-image';
    case 'video': return 'fa-file-video';
    case 'audio': return 'fa-file-audio';
    case 'pdf': return 'fa-file-pdf';
    case 'document': return 'fa-file-word';
    case 'spreadsheet': return 'fa-file-excel';
    case 'presentation': return 'fa-file-powerpoint';
    case 'archive': return 'fa-file-archive';
    case 'text': return 'fa-file-alt';
    default: return 'fa-file';
  }
}

/**
 * Group files by type/category
 */
export function groupFilesByType(files: FileDto[]): Record<string, FileDto[]> {
  return files.reduce((groups, file) => {
    const category = getMimeTypeCategory(file.mimeType);
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(file);
    return groups;
  }, {} as Record<string, FileDto[]>);
}

/**
 * Sort files by various criteria
 */
export function sortFiles(files: FileDto[], sortBy: 'name' | 'size' | 'date' | 'type', ascending: boolean = true): FileDto[] {
  const sorted = [...files].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'size':
        comparison = a.fileSize - b.fileSize;
        break;
      case 'date':
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        break;
      case 'type':
        comparison = a.mimeType.localeCompare(b.mimeType);
        break;
    }

    return ascending ? comparison : -comparison;
  });

  return sorted;
}

/**
 * Filter files by search query
 */
export function filterFiles(files: FileDto[], query: string): FileDto[] {
  if (!query.trim()) return files;

  const searchTerm = query.toLowerCase();

  return files.filter(file =>
    file.name.toLowerCase().includes(searchTerm) ||
    file.displayName?.toLowerCase().includes(searchTerm) ||
    file.description?.toLowerCase().includes(searchTerm) ||
    file.tags?.some(tag => tag.toLowerCase().includes(searchTerm)) ||
    file.mimeType.toLowerCase().includes(searchTerm)
  );
}

/**
 * Calculate total size of files
 */
export function calculateTotalSize(files: FileDto[]): number {
  return files.reduce((total, file) => total + file.fileSize, 0);
}

/**
 * Estimate upload time based on file size and connection speed
 */
export function estimateUploadTime(fileSize: number, speedBytesPerSecond: number = 1024 * 1024): string {
  const seconds = fileSize / speedBytesPerSecond;

  if (seconds < 60) {
    return `${Math.ceil(seconds)} seconds`;
  } else if (seconds < 3600) {
    return `${Math.ceil(seconds / 60)} minutes`;
  } else {
    return `${Math.ceil(seconds / 3600)} hours`;
  }
}

/**
 * Check if browser supports drag and drop
 */
export function supportsDragAndDrop(): boolean {
  const div = document.createElement('div');
  return (('draggable' in div) || ('ondragstart' in div && 'ondrop' in div)) &&
         'FormData' in window &&
         'FileReader' in window;
}

/**
 * Check if browser supports chunked upload features
 */
export function supportsChunkedUpload(): boolean {
  return 'Blob' in window &&
         'File' in window &&
         'FileReader' in window &&
         'crypto' in window &&
         'subtle' in window.crypto;
}

/**
 * Generate a unique filename if a file with the same name exists
 */
export function generateUniqueFilename(originalName: string, existingNames: string[]): string {
  if (!existingNames.includes(originalName)) {
    return originalName;
  }

  const extension = getFileExtension(originalName);
  const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));

  let counter = 1;
  let newName: string;

  do {
    newName = `${nameWithoutExt} (${counter})${extension}`;
    counter++;
  } while (existingNames.includes(newName));

  return newName;
}

/**
 * Export utilities object for easier importing
 */
export const FileUtils = {
  formatFileSize,
  getFileExtension,
  getMimeTypeCategory,
  validateFile,
  validateMimeTypeMatchesExtension,
  sanitizeFilename,
  shouldUseChunkedUpload,
  calculateOptimalChunkSize,
  fileToDataURL,
  createFilePreviewUrl,
  releaseFilePreviewUrl,
  getFileIconClass,
  groupFilesByType,
  sortFiles,
  filterFiles,
  calculateTotalSize,
  estimateUploadTime,
  supportsDragAndDrop,
  supportsChunkedUpload,
  generateUniqueFilename
};
