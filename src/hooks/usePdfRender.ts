import { useEffect, useRef, useState, useCallback } from "react";
import { PDFRenderer } from "@/feature/pdf/pdf-renderer";
import { PDFRenderOptions } from "@/types";
import { useViewerStore } from "@/store/viewer-store";

export interface UsePdfRenderOptions {
  fileUrl?: string;
  autoLoad?: boolean;
}

export function usePdfRender(options: UsePdfRenderOptions = {}) {
  const { fileUrl, autoLoad = true } = options;

  const rendererRef = useRef<PDFRenderer | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [numPages, setNumPages] = useState(0);

  const {
    currentPage,
    scale,
    rotation,
    setTotalPages,
    setLoading: setViewerLoading,
    setError: setViewerError,
  } = useViewerStore();

  // Initialize renderer
  useEffect(() => {
    if (!rendererRef.current) {
      rendererRef.current = new PDFRenderer();
    }

    return () => {
      if (rendererRef.current) {
        rendererRef.current.cleanup();
        rendererRef.current = null;
      }
    };
  }, []);

  // Load document
  const loadDocument = useCallback(
    async (url: string) => {
      if (!rendererRef.current) return;

      setIsLoading(true);
      setViewerLoading(true);
      setError(null);
      setViewerError(null);

      try {
        const document = await rendererRef.current.loadDocument(url);
        const pages = document.numPages;

        setNumPages(pages);
        setTotalPages(pages);
        setError(null);
        setViewerError(null);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to load PDF";
        setError(errorMessage);
        setViewerError(errorMessage);
        setNumPages(0);
        setTotalPages(0);
      } finally {
        setIsLoading(false);
        setViewerLoading(false);
      }
    },
    [setTotalPages, setViewerLoading, setViewerError]
  );

  // Auto-load document when URL changes
  useEffect(() => {
    if (fileUrl && autoLoad) {
      loadDocument(fileUrl);
    }
  }, [fileUrl, autoLoad, loadDocument]);

  // Render page to canvas
  const renderPage = useCallback(
    async (
      pageNumber: number,
      canvas: HTMLCanvasElement,
      renderOptions?: Partial<PDFRenderOptions>
    ) => {
      if (!rendererRef.current) {
        throw new Error("PDF renderer not initialized");
      }

      const options: PDFRenderOptions = {
        scale: renderOptions?.scale ?? scale,
        rotation: renderOptions?.rotation ?? rotation,
      };

      try {
        return await rendererRef.current.renderPage(
          pageNumber,
          canvas,
          options
        );
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to render page";
        throw new Error(errorMessage);
      }
    },
    [scale, rotation]
  );

  // Get page dimensions
  const getPageDimensions = useCallback(
    async (pageNumber: number, pageScale?: number) => {
      if (!rendererRef.current) {
        throw new Error("PDF renderer not initialized");
      }

      return await rendererRef.current.getPageDimensions(
        pageNumber,
        pageScale ?? scale
      );
    },
    [scale]
  );

  // Extract text from page
  const extractTextFromPage = useCallback(async (pageNumber: number) => {
    if (!rendererRef.current) {
      throw new Error("PDF renderer not initialized");
    }

    return await rendererRef.current.extractTextFromPage(pageNumber);
  }, []);

  // Extract text from all pages
  const extractTextFromDocument = useCallback(async () => {
    if (!rendererRef.current) {
      throw new Error("PDF renderer not initialized");
    }

    return await rendererRef.current.extractTextFromDocument();
  }, []);

  // Get document info
  const getDocumentInfo = useCallback(() => {
    if (!rendererRef.current) return null;
    return rendererRef.current.getDocumentInfo();
  }, []);

  // Cleanup
  const cleanup = useCallback(() => {
    if (rendererRef.current) {
      rendererRef.current.cleanup();
    }
    setNumPages(0);
    setError(null);
    setIsLoading(false);
  }, []);

  return {
    // State
    isLoading,
    error,
    numPages,

    // Actions
    loadDocument,
    renderPage,
    getPageDimensions,
    extractTextFromPage,
    extractTextFromDocument,
    getDocumentInfo,
    cleanup,

    // Current render options
    currentPage,
    scale,
    rotation,
  };
}
