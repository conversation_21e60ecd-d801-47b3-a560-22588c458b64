import { useCallback } from "react";
import { useViewerStore } from "@/store/viewer-store";
import { SplitRange } from "@/types";
import { SPLIT_RANGE_COLORS } from "@/lib/constants";
import { PDFOperations } from "@/feature/pdf/pdf-operations";

export function useSplitRange() {
  const {
    splitRanges,
    selectedRange,
    totalPages,
    addSplitRange,
    updateSplitRange,
    removeSplitRange,
    clearSplitRanges,
    setSelectedRange,
  } = useViewerStore();

  // Get next available color
  const getNextColor = useCallback(() => {
    const usedColors = splitRanges.map((range) => range.color);
    const availableColors = SPLIT_RANGE_COLORS.filter(
      (color) => !usedColors.includes(color)
    );
    return availableColors[0] || SPLIT_RANGE_COLORS[0];
  }, [splitRanges]);

  // Create new split range
  const createRange = useCallback(
    (start: number, end: number, name?: string) => {
      const rangeName = name || `Range ${splitRanges.length + 1}`;
      const color = getNextColor();

      const newRange: Omit<SplitRange, "id"> = {
        start,
        end,
        name: rangeName,
        color,
      };

      addSplitRange(newRange);
    },
    [splitRanges.length, getNextColor, addSplitRange]
  );

  // Create range from current page
  const createRangeFromPage = useCallback(
    (pageNumber: number, name?: string) => {
      createRange(pageNumber, pageNumber, name);
    },
    [createRange]
  );

  // Create range from page selection
  const createRangeFromSelection = useCallback(
    (startPage: number, endPage: number, name?: string) => {
      const start = Math.min(startPage, endPage);
      const end = Math.max(startPage, endPage);
      createRange(start, end, name);
    },
    [createRange]
  );

  // Update range
  const updateRange = useCallback(
    (id: string, updates: Partial<SplitRange>) => {
      updateSplitRange(id, updates);
    },
    [updateSplitRange]
  );

  // Delete range
  const deleteRange = useCallback(
    (id: string) => {
      removeSplitRange(id);
    },
    [removeSplitRange]
  );

  // Select range
  const selectRange = useCallback(
    (id: string | null) => {
      setSelectedRange(id);
    },
    [setSelectedRange]
  );

  // Clear all ranges
  const clearAllRanges = useCallback(() => {
    clearSplitRanges();
  }, [clearSplitRanges]);

  // Validate ranges
  const validateRanges = useCallback(() => {
    return PDFOperations.validateSplitRanges(
      splitRanges.map((range) => ({
        start: range.start,
        end: range.end,
        name: range.name,
      })),
      totalPages
    );
  }, [splitRanges, totalPages]);

  // Generate suggested ranges
  const generateSuggestedRanges = useCallback(
    (count: number = 2) => {
      const suggested = PDFOperations.generateSuggestedRanges(
        totalPages,
        count
      );

      // Clear existing ranges and add suggested ones
      clearSplitRanges();

      suggested.forEach((range, index) => {
        const color = SPLIT_RANGE_COLORS[index % SPLIT_RANGE_COLORS.length];
        addSplitRange({
          ...range,
          color,
        });
      });
    },
    [totalPages, clearSplitRanges, addSplitRange]
  );

  // Check if page is in any range
  const isPageInRange = useCallback(
    (pageNumber: number): SplitRange | null => {
      return (
        splitRanges.find(
          (range) => pageNumber >= range.start && pageNumber <= range.end
        ) || null
      );
    },
    [splitRanges]
  );

  // Get range by ID
  const getRangeById = useCallback(
    (id: string): SplitRange | undefined => {
      return splitRanges.find((range) => range.id === id);
    },
    [splitRanges]
  );

  // Get selected range object
  const getSelectedRange = useCallback((): SplitRange | null => {
    return selectedRange ? getRangeById(selectedRange) || null : null;
  }, [selectedRange, getRangeById]);

  // Check if ranges overlap
  const hasOverlappingRanges = useCallback((): boolean => {
    for (let i = 0; i < splitRanges.length; i++) {
      for (let j = i + 1; j < splitRanges.length; j++) {
        const range1 = splitRanges[i];
        const range2 = splitRanges[j];

        if (
          (range1.start <= range2.end && range1.end >= range2.start) ||
          (range2.start <= range1.end && range2.end >= range1.start)
        ) {
          return true;
        }
      }
    }
    return false;
  }, [splitRanges]);

  // Get total pages covered by ranges
  const getTotalPagesCovered = useCallback((): number => {
    const coveredPages = new Set<number>();

    splitRanges.forEach((range) => {
      for (let page = range.start; page <= range.end; page++) {
        coveredPages.add(page);
      }
    });

    return coveredPages.size;
  }, [splitRanges]);

  // Export ranges for splitting
  const exportRanges = useCallback(() => {
    return splitRanges.map((range) => ({
      start: range.start,
      end: range.end,
      name: range.name,
    }));
  }, [splitRanges]);

  return {
    // State
    splitRanges,
    selectedRange,
    totalPages,

    // Actions
    createRange,
    createRangeFromPage,
    createRangeFromSelection,
    updateRange,
    deleteRange,
    selectRange,
    clearAllRanges,

    // Utilities
    validateRanges,
    generateSuggestedRanges,
    isPageInRange,
    getRangeById,
    getSelectedRange,
    hasOverlappingRanges,
    getTotalPagesCovered,
    exportRanges,

    // Computed
    hasRanges: splitRanges.length > 0,
    isValid: validateRanges().isValid,
    validationErrors: validateRanges().errors,
  };
}
