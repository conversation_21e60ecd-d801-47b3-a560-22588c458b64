import { ApiClient } from '../core/apiClient';
import {
  PaginationInfo,
  SortDirection,
  ErrorCode
} from '../types/interfaces';
import { ValidationApiError, ApiError } from '../core/apiClient';

export enum DeletedItemType {
  Folder = 'Folder',
  File = 'File'
}

export interface DeletedItemDto {
  id: string;
  originalId: string;
  name: string;
  itemType: DeletedItemType;
  originalPath: string;
  fileSize?: number;
  mimeType?: string;
  deletedAt: string;
  deletedBy: string;
  deletedByName: string;
  expiresAt: string;
  canRestore: boolean;
  restoreConflicts?: string[];
  parentFolderId?: string;
  originalParentName?: string;
}

export interface DeletedItemsListOptions {
  page?: number;
  pageSize?: number;
  search?: string;
  itemType?: DeletedItemType;
  sortBy?: 'Name' | 'DeletedAt' | 'ExpiresAt' | 'FileSize';
  sortDirection?: SortDirection;
  deletedAfter?: string;
  deletedBefore?: string;
  deletedBy?: string;
}

export interface RecycleBinStatistics {
  totalItems: number;
  totalFiles: number;
  totalFolders: number;
  totalSize: number;
  itemsExpiringSoon: number;
  oldestItemDate?: string;
  newestItemDate?: string;
  retentionDays: number;
}

export interface RestoreResult {
  success: boolean;
  restoredItem?: {
    id: string;
    name: string;
    newPath: string;
  };
  conflicts?: string[];
  error?: string;
}

export interface BulkRestoreRequest {
  itemIds: string[];
  handleConflicts?: 'skip' | 'rename' | 'overwrite';
}

export interface BulkDeleteRequest {
  itemIds: string[];
  confirm?: boolean;
}

export interface BulkOperationResponse {
  totalRequested: number;
  successful: Array<{
    itemId: string;
    itemName: string;
    result?: any;
  }>;
  failed: Array<{
    itemId: string;
    itemName: string;
    error: string;
  }>;
  summary: {
    successCount: number;
    failureCount: number;
    totalProcessed: number;
  };
}

export interface RestoreFeasibilityCheck {
  canRestore: boolean;
  conflicts: Array<{
    type: 'name_conflict' | 'parent_not_found' | 'permission_denied';
    description: string;
    suggestedAction?: string;
  }>;
  requiresConfirmation: boolean;
}

export interface RecycleBinSettings {
  retentionDays: number;
  autoCleanupEnabled: boolean;
  maxItemsPerUser: number;
  notifyBeforeExpiry: boolean;
  notificationDays: number;
}

export class RecycleBinService {
  private client: ApiClient;

  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }

  /**
   * Get deleted items from recycle bin
   * @param options Query options for listing deleted items
   * @returns Paginated list of deleted items
   */
  async getDeletedItems(options?: DeletedItemsListOptions): Promise<{
    items: DeletedItemDto[];
    pagination: PaginationInfo;
  }> {
    const params: Record<string, any> = {};

    if (options?.page) params.page = options.page;
    if (options?.pageSize) params.pageSize = options.pageSize;
    if (options?.search) params.search = options.search;
    if (options?.itemType) params.itemType = options.itemType;
    if (options?.sortBy) params.sortBy = options.sortBy;
    if (options?.sortDirection) params.sortDirection = options.sortDirection;
    if (options?.deletedAfter) params.deletedAfter = options.deletedAfter;
    if (options?.deletedBefore) params.deletedBefore = options.deletedBefore;
    if (options?.deletedBy) params.deletedBy = options.deletedBy;

    try {
      return await this.client.get('/recycle-bin', { params });
    } catch (error) {
      throw this.handleListError(error as ApiError);
    }
  }

  /**
   * Get specific deleted item details
   * @param itemId Deleted item ID
   * @returns Deleted item details
   */
  async getDeletedItem(itemId: string): Promise<DeletedItemDto> {
    this.validateId(itemId, 'itemId');

    try {
      return await this.client.get<DeletedItemDto>(`/recycle-bin/${itemId}`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'Deleted item');
    }
  }

  /**
   * Restore a deleted item
   * @param itemId Deleted item ID
   * @param options Restore options
   * @returns Restore result
   */
  async restoreItem(itemId: string, options?: {
    targetFolderId?: string;
    newName?: string;
    handleConflicts?: 'skip' | 'rename' | 'overwrite';
  }): Promise<RestoreResult> {
    this.validateId(itemId, 'itemId');
    this.validateRestoreOptions(options);

    try {
      return await this.client.post<RestoreResult>(`/recycle-bin/${itemId}/restore`, options || {});
    } catch (error) {
      throw this.handleRestoreError(error as ApiError);
    }
  }

  /**
   * Permanently delete an item from recycle bin
   * @param itemId Deleted item ID
   */
  async permanentlyDeleteItem(itemId: string): Promise<void> {
    this.validateId(itemId, 'itemId');

    try {
      await this.client.delete(`/recycle-bin/${itemId}`);
    } catch (error) {
      throw this.handlePermanentDeleteError(error as ApiError);
    }
  }

  /**
   * Get recycle bin statistics
   * @returns Statistics about deleted items
   */
  async getStatistics(): Promise<RecycleBinStatistics> {
    try {
      return await this.client.get<RecycleBinStatistics>('/recycle-bin/statistics');
    } catch (error) {
      throw this.handleStatisticsError(error as ApiError);
    }
  }

  /**
   * Empty recycle bin (delete expired items)
   * @param force Whether to delete all items regardless of expiry
   * @returns Cleanup results
   */
  async emptyRecycleBin(force: boolean = false): Promise<{
    deletedItems: number;
    freedSpace: number;
    message: string;
  }> {
    const params: Record<string, any> = {};
    if (force) params.force = force;

    try {
      return await this.client.delete('/recycle-bin/empty', { params });
    } catch (error) {
      throw this.handleEmptyError(error as ApiError);
    }
  }

  /**
   * Bulk restore multiple items
   * @param request Bulk restore request
   * @returns Bulk operation results
   */
  async bulkRestore(request: BulkRestoreRequest): Promise<BulkOperationResponse> {
    this.validateBulkRestoreRequest(request);

    try {
      return await this.client.post<BulkOperationResponse>('/recycle-bin/bulk/restore', request);
    } catch (error) {
      throw this.handleBulkError(error as ApiError);
    }
  }

  /**
   * Bulk permanently delete multiple items
   * @param request Bulk delete request
   * @returns Bulk operation results
   */
  async bulkPermanentlyDelete(request: BulkDeleteRequest): Promise<BulkOperationResponse> {
    this.validateBulkDeleteRequest(request);

    try {
      return await this.client.post<BulkOperationResponse>('/recycle-bin/bulk/delete', request);
    } catch (error) {
      throw this.handleBulkError(error as ApiError);
    }
  }

  /**
   * Check if an item can be restored (feasibility check)
   * @param itemId Deleted item ID
   * @param targetFolderId Optional target folder for restore
   * @returns Restore feasibility information
   */
  async checkRestoreFeasibility(itemId: string, targetFolderId?: string): Promise<RestoreFeasibilityCheck> {
    this.validateId(itemId, 'itemId');

    const params: Record<string, any> = {};
    if (targetFolderId) params.targetFolderId = targetFolderId;

    try {
      return await this.client.get<RestoreFeasibilityCheck>(`/recycle-bin/${itemId}/check-restore`, { params });
    } catch (error) {
      throw this.handleFeasibilityError(error as ApiError);
    }
  }

  /**
   * Search deleted items
   * @param query Search query
   * @param options Search options
   * @returns Search results
   */
  async searchDeletedItems(query: string, options?: {
    itemType?: DeletedItemType;
    deletedAfter?: string;
    deletedBefore?: string;
    maxResults?: number;
  }): Promise<{
    items: DeletedItemDto[];
    totalResults: number;
    searchQuery: string;
  }> {
    if (!query || query.trim().length === 0) {
      throw new ValidationApiError('Search query is required', [
        { field: 'query', message: 'Search query cannot be empty', code: 'REQUIRED' }
      ]);
    }

    const params: Record<string, any> = { query };
    if (options?.itemType) params.itemType = options.itemType;
    if (options?.deletedAfter) params.deletedAfter = options.deletedAfter;
    if (options?.deletedBefore) params.deletedBefore = options.deletedBefore;
    if (options?.maxResults) params.maxResults = options.maxResults;

    try {
      return await this.client.get('/recycle-bin/search', { params });
    } catch (error) {
      throw this.handleSearchError(error as ApiError);
    }
  }

  /**
   * Get recycle bin settings
   * @returns Current recycle bin settings
   */
  async getSettings(): Promise<RecycleBinSettings> {
    try {
      return await this.client.get<RecycleBinSettings>('/recycle-bin/settings');
    } catch (error) {
      throw this.handleSettingsError(error as ApiError);
    }
  }

  /**
   * Update recycle bin settings
   * @param settings Settings to update
   * @returns Updated settings
   */
  async updateSettings(settings: Partial<RecycleBinSettings>): Promise<RecycleBinSettings> {
    this.validateSettings(settings);

    try {
      return await this.client.put<RecycleBinSettings>('/recycle-bin/settings', settings);
    } catch (error) {
      throw this.handleSettingsUpdateError(error as ApiError);
    }
  }

  /**
   * Get items expiring soon
   * @param days Number of days ahead to check (default: 7)
   * @returns Items expiring within the specified days
   */
  async getItemsExpiringSoon(days: number = 7): Promise<DeletedItemDto[]> {
    if (!Number.isInteger(days) || days < 1 || days > 365) {
      throw new ValidationApiError('Invalid days parameter', [
        { field: 'days', message: 'Days must be between 1 and 365', code: 'INVALID_VALUE' }
      ]);
    }

    try {
      return await this.client.get<DeletedItemDto[]>('/recycle-bin/expiring-soon', {
        params: { days }
      });
    } catch (error) {
      throw this.handleExpiringError(error as ApiError);
    }
  }

  // Validation methods

  private validateId(id: string, fieldName: string): void {
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new ValidationApiError(`Invalid ${fieldName}`, [
        { field: fieldName, message: `${fieldName} is required and must be a non-empty string`, code: 'REQUIRED' }
      ]);
    }
  }

  private validateRestoreOptions(options?: {
    targetFolderId?: string;
    newName?: string;
    handleConflicts?: 'skip' | 'rename' | 'overwrite';
  }): void {
    if (!options) return;

    if (options.targetFolderId) {
      this.validateId(options.targetFolderId, 'targetFolderId');
    }

    if (options.newName && (typeof options.newName !== 'string' || options.newName.trim().length === 0)) {
      throw new ValidationApiError('Invalid new name', [
        { field: 'newName', message: 'New name must be a non-empty string', code: 'INVALID_VALUE' }
      ]);
    }

    if (options.handleConflicts && !['skip', 'rename', 'overwrite'].includes(options.handleConflicts)) {
      throw new ValidationApiError('Invalid conflict handling option', [
        { field: 'handleConflicts', message: 'Conflict handling must be skip, rename, or overwrite', code: 'INVALID_VALUE' }
      ]);
    }
  }

  private validateBulkRestoreRequest(request: BulkRestoreRequest): void {
    if (!request) {
      throw new ValidationApiError('Bulk restore request is required', [
        { field: 'request', message: 'Request data is required', code: 'REQUIRED' }
      ]);
    }

    if (!request.itemIds || !Array.isArray(request.itemIds) || request.itemIds.length === 0) {
      throw new ValidationApiError('Item IDs are required', [
        { field: 'itemIds', message: 'At least one item ID is required', code: 'REQUIRED' }
      ]);
    }

    if (request.itemIds.length > 50) {
      throw new ValidationApiError('Too many items', [
        { field: 'itemIds', message: 'Cannot process more than 50 items at once', code: 'TOO_MANY_ITEMS' }
      ]);
    }

    request.itemIds.forEach((id, index) => {
      if (!id || typeof id !== 'string' || id.trim().length === 0) {
        throw new ValidationApiError('Invalid item ID', [
          { field: `itemIds[${index}]`, message: 'Item ID must be a non-empty string', code: 'INVALID_VALUE' }
        ]);
      }
    });

    if (request.handleConflicts && !['skip', 'rename', 'overwrite'].includes(request.handleConflicts)) {
      throw new ValidationApiError('Invalid conflict handling option', [
        { field: 'handleConflicts', message: 'Conflict handling must be skip, rename, or overwrite', code: 'INVALID_VALUE' }
      ]);
    }
  }

  private validateBulkDeleteRequest(request: BulkDeleteRequest): void {
    if (!request) {
      throw new ValidationApiError('Bulk delete request is required', [
        { field: 'request', message: 'Request data is required', code: 'REQUIRED' }
      ]);
    }

    if (!request.itemIds || !Array.isArray(request.itemIds) || request.itemIds.length === 0) {
      throw new ValidationApiError('Item IDs are required', [
        { field: 'itemIds', message: 'At least one item ID is required', code: 'REQUIRED' }
      ]);
    }

    if (request.itemIds.length > 50) {
      throw new ValidationApiError('Too many items', [
        { field: 'itemIds', message: 'Cannot process more than 50 items at once', code: 'TOO_MANY_ITEMS' }
      ]);
    }

    request.itemIds.forEach((id, index) => {
      if (!id || typeof id !== 'string' || id.trim().length === 0) {
        throw new ValidationApiError('Invalid item ID', [
          { field: `itemIds[${index}]`, message: 'Item ID must be a non-empty string', code: 'INVALID_VALUE' }
        ]);
      }
    });
  }

  private validateSettings(settings: Partial<RecycleBinSettings>): void {
    if (!settings || Object.keys(settings).length === 0) {
      throw new ValidationApiError('Settings are required', [
        { field: 'settings', message: 'At least one setting must be provided', code: 'REQUIRED' }
      ]);
    }

    if (settings.retentionDays !== undefined) {
      if (!Number.isInteger(settings.retentionDays) || settings.retentionDays < 1 || settings.retentionDays > 365) {
        throw new ValidationApiError('Invalid retention days', [
          { field: 'retentionDays', message: 'Retention days must be between 1 and 365', code: 'INVALID_VALUE' }
        ]);
      }
    }

    if (settings.maxItemsPerUser !== undefined) {
      if (!Number.isInteger(settings.maxItemsPerUser) || settings.maxItemsPerUser < 1) {
        throw new ValidationApiError('Invalid max items per user', [
          { field: 'maxItemsPerUser', message: 'Max items per user must be a positive integer', code: 'INVALID_VALUE' }
        ]);
      }
    }

    if (settings.notificationDays !== undefined) {
      if (!Number.isInteger(settings.notificationDays) || settings.notificationDays < 1 || settings.notificationDays > 30) {
        throw new ValidationApiError('Invalid notification days', [
          { field: 'notificationDays', message: 'Notification days must be between 1 and 30', code: 'INVALID_VALUE' }
        ]);
      }
    }
  }

  // Error handling methods

  private handleListError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to view recycle bin', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleNotFoundError(error: ApiError, resourceType: string): ApiError {
    if (error.statusCode === 404) {
      return new ApiError(`${resourceType} not found`, 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    return error;
  }

  private handleRestoreError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Cannot restore item due to conflicts', 409, 'RESTORE_CONFLICT', error.correlationId);
    }
    if (error.statusCode === 410) {
      return new ApiError('Item has expired and cannot be restored', 410, 'ITEM_EXPIRED', error.correlationId);
    }
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to restore item', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return this.handleNotFoundError(error, 'Deleted item');
  }

  private handlePermanentDeleteError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to permanently delete item', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return this.handleNotFoundError(error, 'Deleted item');
  }

  private handleStatisticsError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to view recycle bin statistics', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleEmptyError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to empty recycle bin', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleBulkError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions for bulk operation', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 413) {
      return new ApiError('Too many items for bulk operation', 413, 'TOO_MANY_ITEMS', error.correlationId);
    }
    return error;
  }

  private handleFeasibilityError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to check restore feasibility', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return this.handleNotFoundError(error, 'Deleted item');
  }

  private handleSearchError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to search recycle bin', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleSettingsError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to view recycle bin settings', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleSettingsUpdateError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to update recycle bin settings', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleExpiringError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to view expiring items', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }
} 