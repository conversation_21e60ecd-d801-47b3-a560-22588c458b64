import { ApiClient, ApiError } from '../core/apiClient';
import {
  UserProfile,
  UpdateUserProfileRequest
} from '@/types/user';

/**
 * User Service for managing user information and profile operations
 *
 * Provides methods for:
 * - Getting current user information
 * - Updating user profile
 * - Managing user preferences
 */
export class UserService {
  private getTokenFn?: () => Promise<string | null>;

  constructor(_apiClient: ApiClient, getAccessToken?: () => Promise<string | null>) {
    this.getTokenFn = getAccessToken;
  }

  /**
   * Get current user information from Identity Server OIDC userinfo endpoint
   * @returns Current user profile information
   *
   * @example
   * ```typescript
   * const userService = new UserService(apiClient);
   * const userInfo = await userService.getCurrentUser();
   * console.log('User:', userInfo.name, userInfo.email);
   * ```
   */
  async getCurrentUser(): Promise<UserProfile> {
    try {
      // Call Identity Server OIDC userinfo endpoint directly
      const token = await this.getAccessToken();
      const response = await fetch('https://sso.veasy.vn/connect/userinfo', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const userInfo: UserProfile = await response.json();
      return userInfo;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError(
        'Failed to retrieve user information from Identity Server',
        (error as any).statusCode || 500,
        'USER_INFO_ERROR',
        (error as any).correlationId
      );
    }
  }

  /**
   * Get access token from current user session
   * @private
   */
  private async getAccessToken(): Promise<string> {
    // Use the provided token function if available
    if (this.getTokenFn) {
      const token = await this.getTokenFn();
      if (token) {
        return token;
      }
    }

    // Fallback: try to get token from storage
    if (typeof window !== 'undefined') {
      const token = sessionStorage.getItem('oidc.access_token') ||
                   localStorage.getItem('oidc.access_token');
      if (token) {
        return token;
      }
    }

    throw new Error('No access token available');
  }

  /**
   * Update current user profile information
   * Note: This functionality is not available with OIDC userinfo endpoint
   * Profile updates should be handled through the Identity Server admin interface
   */
  async updateProfile(_updateData: UpdateUserProfileRequest): Promise<UserProfile> {
    throw new ApiError(
      'Profile updates are not supported via OIDC userinfo endpoint. Please use Identity Server admin interface.',
      501,
      'NOT_IMPLEMENTED'
    );
  }

  /**
   * Upload user avatar
   * Note: This functionality is not available with OIDC userinfo endpoint
   * Avatar uploads should be handled through the Identity Server admin interface
   */
  async uploadAvatar(_file: File): Promise<UserProfile> {
    throw new ApiError(
      'Avatar uploads are not supported via OIDC userinfo endpoint. Please use Identity Server admin interface.',
      501,
      'NOT_IMPLEMENTED'
    );
  }

  /**
   * Get user activity log
   * Note: This functionality is not available with OIDC userinfo endpoint
   * Activity logs should be accessed through the Identity Server admin interface
   */
  async getUserActivity(_limit: number = 50, _offset: number = 0): Promise<{
    activities: Array<{
      id: string;
      action: string;
      description: string;
      timestamp: string;
      ipAddress?: string;
      userAgent?: string;
    }>;
    total: number;
    hasMore: boolean;
  }> {
    throw new ApiError(
      'User activity logs are not supported via OIDC userinfo endpoint. Please use Identity Server admin interface.',
      501,
      'NOT_IMPLEMENTED'
    );
  }

  /**
   * Change user password
   * Note: This functionality is not available with OIDC userinfo endpoint
   * Password changes should be handled through the Identity Server admin interface
   */
  async changePassword(_currentPassword: string, _newPassword: string): Promise<{ success: boolean; message: string }> {
    throw new ApiError(
      'Password changes are not supported via OIDC userinfo endpoint. Please use Identity Server admin interface.',
      501,
      'NOT_IMPLEMENTED'
    );
  }

  /**
   * Delete user account
   * Note: This functionality is not available with OIDC userinfo endpoint
   * Account deletion should be handled through the Identity Server admin interface
   */
  async deleteAccount(_password: string): Promise<{ success: boolean; message: string }> {
    throw new ApiError(
      'Account deletion is not supported via OIDC userinfo endpoint. Please use Identity Server admin interface.',
      501,
      'NOT_IMPLEMENTED'
    );
  }
}
