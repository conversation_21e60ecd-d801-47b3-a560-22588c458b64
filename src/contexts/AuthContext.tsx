"use client";

import React, { create<PERSON>ontext, useContext, ReactNode, useEffect, useState } from "react";
import {
  AuthP<PERSON>ider,
  AuthProviderProps,
  useAuth as useOidcAuth,
} from "react-oidc-context";
import { oidcConfig, env } from "@/lib/oidcConfig";
import { UserService } from "@/api/services/userService";
import { ApiClient } from "@/api/core/apiClient";
import { UserProfile } from "@/types/user";
import { Log } from "oidc-client-ts";
import { 
  logOidcConfig, 
  debugAuthState, 
  handleAuthError, 
  clearAuthState,
  validateOidcConfig,
  isTokenExpired,
  getUserRoles,
  hasRole,
  hasAnyRole
} from "@/lib/authUtils";

// Enable detailed logging in development environment
if (process.env.NODE_ENV === "development") {
  Log.setLogger(console);
  Log.setLevel(Log.DEBUG);
}

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any;
  userProfile: UserProfile | null;
  isLoadingProfile: boolean;
  login: () => void;
  logout: () => void;
  error: any | null;
  refreshUserProfile: () => Promise<void>;
  // Additional utility functions
  getUserRoles: () => string[];
  hasRole: (role: string) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
  isTokenExpired: () => boolean;
  clearAuthState: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProviderWrapper: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  // Validate OIDC configuration on startup
  useEffect(() => {
    if (!validateOidcConfig()) {
      console.error("🚨 OIDC configuration validation failed");
    }
  }, []);

  return (
    <AuthProvider
      {...oidcConfig}
      onSigninCallback={() => {
        // Clear the URL of OIDC parameters after successful callback
        if (typeof window !== "undefined") {
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname
          );
        }
      }}
    >
      <AuthContextProvider>{children}</AuthContextProvider>
    </AuthProvider>
  );
};

const AuthContextProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const auth = useOidcAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);
  const [userService, setUserService] = useState<UserService | null>(null);

  // Debug logging in development
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      logOidcConfig();
      // Delay debug state to allow for initial auth state to settle
      setTimeout(() => {
        debugAuthState();
      }, 1000);
    }
  }, []);

  // Enhanced error handling with specific error types
  useEffect(() => {
    if (auth.error) {
      console.error("🚨 OIDC Error Details:", {
        message: auth.error.message,
        name: auth.error.name,
        stack: auth.error.stack,
      });

      // Handle specific error types
      if (auth.error.message?.includes("No state in response") ||
          auth.error.message?.includes("No matching state found") ||
          auth.error.message?.includes("Invalid state")) {
        console.error("🚨 State validation error detected. Possible causes:");
        console.error("- Browser cookie/storage issues");
        console.error("- Cross-origin problems");
        console.error("- Incorrect response_mode setting");
        console.error("- Multiple UserManager instances");
        console.error("- Identity Provider configuration mismatch");

        // Clear corrupted state
        clearAuthState();
      }

      if (auth.error.message?.includes("Network request failed") ||
          auth.error.message?.includes("Failed to fetch")) {
        console.error("🚨 Network error detected. Check:");
        console.error("- Identity Provider availability");
        console.error("- CORS configuration");
        console.error("- Network connectivity");
      }

      if (auth.error.message?.includes("token") && 
          auth.error.message?.includes("expired")) {
        console.error("🚨 Token expiration detected");
      }

      // Use centralized error handler
      handleAuthError(auth.error);
    }
  }, [auth.error]);

  // Handle successful user loading
  useEffect(() => {
    if (auth.user) {
      console.log("✅ User loaded successfully:", {
        name: auth.user.profile?.name,
        email: auth.user.profile?.email,
        sub: auth.user.profile?.sub,
        expires_at: auth.user.expires_at ? new Date(auth.user.expires_at * 1000).toISOString() : null,
      });
    }
  }, [auth.user]);

  // Initialize UserService when user is authenticated
  useEffect(() => {
    if (auth.isAuthenticated && auth.user && !userService) {
      const apiClient = new ApiClient();
      // Create function to get access token from OIDC user
      const getAccessToken = async () => {
        return auth.user?.access_token || null;
      };
      const newUserService = new UserService(apiClient, getAccessToken);
      setUserService(newUserService);
    }
  }, [auth.isAuthenticated, auth.user, userService]);

  // Function to refresh user profile
  const refreshUserProfile = async () => {
    if (!userService || !auth.isAuthenticated) {
      return;
    }

    setIsLoadingProfile(true);
    try {
      const profile = await userService.getCurrentUser();
      setUserProfile(profile);
      console.log("✅ User profile loaded:", profile);
    } catch (error) {
      console.error("❌ Failed to load user profile:", error);
      setUserProfile(null);
    } finally {
      setIsLoadingProfile(false);
    }
  };

  // Load user profile when UserService is available
  useEffect(() => {
    if (userService && auth.isAuthenticated && !userProfile && !isLoadingProfile) {
      refreshUserProfile();
    }
  }, [userService, auth.isAuthenticated, userProfile, isLoadingProfile]);

  // Enhanced auth state logging
  useEffect(() => {
    console.log("🔐 Auth State Changed:", {
      isAuthenticated: auth.isAuthenticated,
      isLoading: auth.isLoading,
      hasUser: !!auth.user,
      hasError: !!auth.error,
      errorType: auth.error ? auth.error.constructor.name : null,
      errorMessage: auth.error ? auth.error.message : null,
      userRoles: auth.user ? getUserRoles(auth.user) : [],
      isTokenExpired: auth.user ? isTokenExpired(auth.user) : null,
    });

    // Handle successful authentication
    if (auth.isAuthenticated && auth.user && !auth.isLoading) {
      console.log("✅ User authenticated successfully:", {
        name: auth.user.profile?.name,
        email: auth.user.profile?.email,
        sub: auth.user.profile?.sub,
        roles: getUserRoles(auth.user),
        expires_at: auth.user.expires_at ? new Date(auth.user.expires_at * 1000).toISOString() : null,
      });
    }
  }, [auth.isAuthenticated, auth.isLoading, auth.user, auth.error]);

  // Listen for custom logout events
  useEffect(() => {
    const handleCustomLogout = () => {
      console.log("🔔 Custom logout event received");
      setUserProfile(null);
      setUserService(null);
      // Trigger OIDC logout
      auth.signoutRedirect().catch(error => {
        console.error("Failed to redirect to logout:", error);
      });
    };

    window.addEventListener('userSignout', handleCustomLogout);
    return () => {
      window.removeEventListener('userSignout', handleCustomLogout);
    };
  }, [auth]);

  const contextValue: AuthContextType = {
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    user: auth.user,
    userProfile,
    isLoadingProfile,
    refreshUserProfile,
    login: () => {
      try {
        // Save current URL as return URL if not already set
        if (
          typeof window !== "undefined" &&
          !sessionStorage.getItem("auth_return_url")
        ) {
          const currentPath = window.location.pathname;
          if (currentPath !== "/auth/login" && currentPath !== "/auth/callback") {
            sessionStorage.setItem("auth_return_url", currentPath);
            console.log("🔐 Saved current path as return URL:", currentPath);
          }
        }

        // Initiate login
        console.log("🔑 Initiating signin redirect...");
        auth.signinRedirect({
          state: { returnUrl: window.location.pathname }
        });
      } catch (error) {
        console.error("🚨 Login initiation failed:", error);
        handleAuthError(error as Error);
      }
    },
    logout: async () => {
      try {
        console.log("🚪 Initiating logout...");
        
        // Clear local state immediately
        setUserProfile(null);
        setUserService(null);
        
        // Clear any cached data
        if (typeof window !== "undefined") {
          // Clear auth return URL
          sessionStorage.removeItem("auth_return_url");
          
          // Clear any other auth-related data
                     try {
             localStorage.removeItem(`oidc.user:${env.oidcAuthority}:${env.oidcClientId}`);
           } catch (e) {
             console.warn("Could not clear localStorage:", e);
           }
        }
        
        // Attempt OIDC signout redirect with explicit post_logout_redirect_uri
        try {
          await auth.signoutRedirect({
            post_logout_redirect_uri: `${window.location.origin}/`
          });
        } catch (signoutError) {
          console.warn("🚨 OIDC signout redirect failed:", signoutError);
          
          // Fallback: try removing user and redirecting manually
          try {
            await auth.removeUser();
            console.log("✅ User removed from storage");
          } catch (removeError) {
            console.warn("Failed to remove user:", removeError);
          }
          
          // Clear all auth state and redirect manually
          console.log("🔄 Performing manual logout cleanup...");
          clearAuthState();
          
          // Force redirect to home page
          if (typeof window !== "undefined") {
            window.location.href = "/";
          }
        }
        
      } catch (error) {
        console.error("🚨 Logout failed:", error);
        
        // Emergency fallback: force cleanup and redirect
        console.log("🆘 Emergency logout cleanup...");
        
        // Clear everything
        setUserProfile(null);
        setUserService(null);
        clearAuthState();
        
        // Force browser redirect
        if (typeof window !== "undefined") {
          // Clear relevant storage
                     try {
             sessionStorage.clear();
             localStorage.removeItem(`oidc.user:${env.oidcAuthority}:${env.oidcClientId}`);
           } catch (e) {
             console.warn("Could not clear storage:", e);
           }
          
          // Redirect to home
          window.location.href = "/";
        }
        
        handleAuthError(error as Error);
      }
    },
    error: auth.error ?? null,
    
    // Utility functions
    getUserRoles: () => getUserRoles(auth.user ?? null),
    hasRole: (role: string) => hasRole(auth.user ?? null, role),
    hasAnyRole: (roles: string[]) => hasAnyRole(auth.user ?? null, roles),
    isTokenExpired: () => isTokenExpired(auth.user ?? null),
    clearAuthState,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProviderWrapper");
  }
  return context;
};
